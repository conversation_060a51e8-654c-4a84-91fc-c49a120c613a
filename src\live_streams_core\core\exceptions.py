"""
直播聚合核心库 - 异常定义

定义所有平台相关的异常类
"""


class LiveStreamException(Exception):
    """基础异常类"""
    pass


class PlatformException(LiveStreamException):
    """平台相关异常"""
    pass


class AuthenticationError(PlatformException):
    """认证失败"""
    pass


class RateLimitError(PlatformException):
    """频率限制"""
    pass


class NetworkException(LiveStreamException):
    """网络相关异常"""
    pass


class TimeoutError(NetworkException):
    """超时错误"""
    pass


class DataException(LiveStreamException):
    """数据相关异常"""
    pass


class ParseError(DataException):
    """解析错误"""
    pass


class DecryptError(DataException):
    """解密错误"""
    pass


class ConfigError(LiveStreamException):
    """配置错误"""
    pass 