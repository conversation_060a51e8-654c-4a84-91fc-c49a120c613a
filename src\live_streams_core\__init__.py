"""
Live Streams Core - 多平台直播聚合核心库

提供统一的接口访问多个直播平台的数据
"""

__version__ = "0.1.0"
__author__ = "Live Streams Core Team"

# 导出核心组件
from .core.base import PlatformAdapter, ListResponse
from .core.exceptions import (
    AuthenticationError,
    ConfigError,
    DataException,
    DecryptError,
    LiveStreamException,
    NetworkException,
    ParseError,
    PlatformException,
    RateLimitError,
    TimeoutError,
)
from .core.models import (
    Platform,
    ResponseWrapper,
    StreamQuality,
    StreamType,
    UnifiedStreamer,
)

# 导出工具类
from .utils.crypto import CryptoUtils
from .utils.http_client import AsyncHTTPClient, create_client

from .platforms import (
    SwagAdapter,
    XiaohuliAdapter,
    FanqieAdapter,
    LvchaAdapter,
    PandaAdapter,
    StripchatAdapter
)

__all__ = [
    # 版本信息
    "__version__",
    "__author__",
    
    # 核心基类
    "PlatformAdapter",
    
    # 数据模型
    "Platform",
    "StreamType",
    "StreamQuality",
    "UnifiedStreamer",
    "ResponseWrapper",
    "ListResponse",
    
    # 异常类
    "LiveStreamException",
    "PlatformException",
    "AuthenticationError",
    "RateLimitError",
    "NetworkException",
    "TimeoutError",
    "DataException",
    "ParseError",
    "DecryptError",
    "ConfigError",
    
    # 工具类
    "AsyncHTTPClient",
    "create_client",
    "CryptoUtils",
    
    # 平台适配器
    "SwagAdapter",
    "XiaohuliAdapter",
    "FanqieAdapter",
    "LvchaAdapter",
    "PandaAdapter",
    "StripchatAdapter",
] 