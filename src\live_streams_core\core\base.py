"""
直播聚合核心库 - 平台适配器基类

定义所有平台适配器的基类接口
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional

from loguru import logger

from ..utils.http_client import AsyncHTTPClient
from .exceptions import PlatformException
from .models import ListResponse, Platform, StreamQuality, UnifiedStreamer


class PlatformAdapter(ABC):
    """平台适配器基类 - 支持统一接口和原始数据访问"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.platform = self._get_platform()
        self.logger = logger.bind(platform=self.platform.value)
        self.base_url = self._get_base_url()
        self.http_client: Optional[AsyncHTTPClient] = None
        self._setup()
    
    def _setup(self) -> None:
        """初始化设置"""
        from ..utils.http_client import AsyncHTTPClient
        
        self.http_client = AsyncHTTPClient(
            base_url=self.base_url,
            headers=self._build_headers(),
            timeout=self.config.get("timeout", 30.0),
            proxy=self.config.get("proxy"),
            http2=self.config.get("http2", True)
        )
    
    @abstractmethod
    def _get_platform(self) -> Platform:
        """获取平台标识"""
        pass
    
    @abstractmethod
    def _get_base_url(self) -> str:
        """获取基础URL"""
        pass
    
    def _build_headers(self) -> Dict[str, str]:
        """构建请求头 - 子类可以覆盖"""
        return {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Accept": "application/json, text/plain, */*",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Accept-Encoding": "gzip, deflate, br",
        }
    
    # ========== 统一接口 ==========
    
    @abstractmethod
    async def get_live_list(
        self, 
        category: str = "hot",
        page: int = 1,
        size: int = 20,
        **kwargs
    ) -> List[UnifiedStreamer]:
        """获取直播列表 - 只返回统一格式"""
        pass
    
    async def get_live_list_with_raw(
        self,
        category: str = "hot",
        page: int = 1,
        size: int = 20,
        **kwargs
    ) -> ListResponse[UnifiedStreamer]:
        """获取直播列表 - 返回统一格式和原始数据"""
        self.logger.info(
            "获取直播列表（含原始数据）", 
            category=category, 
            page=page, 
            size=size
        )
        
        # 获取原始数据
        raw_response = await self._fetch_live_list_raw(
            category=category,
            page=page,
            size=size,
            **kwargs
        )
        
        # 解析数据
        raw_items = self._extract_items_from_response(raw_response)
        streamers = []
        
        for item in raw_items:
            try:
                streamer = self.parse_streamer(item)
                # 保存原始数据到模型中
                streamer.raw_data = item
                streamers.append(streamer)
            except Exception as e:
                self.logger.error(
                    "解析主播数据失败", 
                    error=str(e), 
                    error_type=type(e).__name__,
                    raw_data=item
                )
                # 打印详细堆栈以便调试
                import traceback
                traceback.print_exc()
        
        # 构建响应
        return ListResponse(
            items=streamers,
            raw_items=raw_items,
            total=self._extract_total_from_response(raw_response),
            page=page,
            page_size=size,
            has_more=self._check_has_more(raw_response, page, size),
            platform=self.platform.value
        )
    
    @abstractmethod
    async def get_stream_url(self, room_id: str) -> List[StreamQuality]:
        """获取直播流地址"""
        pass
    
    @abstractmethod
    def parse_streamer(self, raw_data: Dict[str, Any]) -> UnifiedStreamer:
        """解析原始数据为统一格式"""
        pass
    
    # ========== 原始数据访问 ==========
    
    async def raw_request(
        self,
        method: str,
        endpoint: str,
        **kwargs
    ) -> Dict[str, Any]:
        """通用的原始请求方法"""
        self.logger.debug(f"原始请求", method=method, endpoint=endpoint)
        
        if not self.http_client:
            raise PlatformException("HTTP客户端未初始化")
        
        url = endpoint if endpoint.startswith("http") else endpoint
        
        try:
            response = await self.http_client.request(method, url, **kwargs)
            response.raise_for_status()
            
            # httpx会自动处理所有压缩格式（包括brotli、gzip、deflate）
            # 直接使用response.json()即可
            try:
                data = response.json()
            except Exception as e:
                # 如果自动解析失败，尝试手动处理
                self.logger.warning(f"JSON解析失败，尝试手动处理", error=str(e))
                
                # 获取原始文本内容
                try:
                    text = response.text
                    import json
                    data = json.loads(text)
                except Exception as e2:
                    self.logger.error(f"手动JSON解析也失败", error=str(e2))
                    raise PlatformException(f"响应解析失败: {str(e2)}")
            
            self.logger.debug(f"原始响应", status=response.status_code)
            return data
            
        except Exception as e:
            self.logger.error(f"原始请求失败", error=str(e), url=url)
            raise
    
    @abstractmethod
    async def _fetch_live_list_raw(
        self,
        category: str,
        page: int,
        size: int,
        **kwargs
    ) -> Dict[str, Any]:
        """获取直播列表的原始响应"""
        pass
    
    def _extract_items_from_response(self, response: Dict[str, Any]) -> List[Dict[str, Any]]:
        """从响应中提取数据项列表 - 子类可覆盖"""
        # 常见的数据结构
        if "data" in response and isinstance(response["data"], list):
            return response["data"]
        elif "data" in response and isinstance(response["data"], dict):
            # 数据可能在 data.list 或 data.items 中
            if "list" in response["data"]:
                return response["data"]["list"]
            elif "items" in response["data"]:
                return response["data"]["items"]
        elif "list" in response:
            return response["list"]
        elif "items" in response:
            return response["items"]
        
        # 如果找不到，返回空列表
        self.logger.warning("无法从响应中提取数据列表", response_keys=list(response.keys()))
        return []
    
    def _extract_total_from_response(self, response: Dict[str, Any]) -> Optional[int]:
        """从响应中提取总数 - 子类可覆盖"""
        # 直接在响应根级别查找
        if "total" in response:
            return response["total"]
        elif "totalCount" in response:
            return response["totalCount"]
        elif "count" in response:
            return response["count"]
        
        # 在 data 中查找
        if "data" in response and isinstance(response["data"], dict):
            if "total" in response["data"]:
                return response["data"]["total"]
            elif "totalCount" in response["data"]:
                return response["data"]["totalCount"]
            elif "count" in response["data"]:
                return response["data"]["count"]
        
        return None
    
    def _check_has_more(self, response: Dict[str, Any], page: int, size: int) -> bool:
        """检查是否有更多数据 - 子类可覆盖"""
        # 直接的标志
        if "hasMore" in response:
            return response["hasMore"]
        elif "has_more" in response:
            return response["has_more"]
        elif "hasNext" in response:
            return response["hasNext"]
        
        # 基于total计算
        total = self._extract_total_from_response(response)
        if total is not None:
            return page * size < total
        
        # 基于返回数据量判断
        items = self._extract_items_from_response(response)
        return len(items) >= size
    
    # ========== 辅助方法 ==========
    
    def _build_unified_id(self, user_id: str) -> str:
        """构建统一ID"""
        return f"{self.platform.value}_{user_id}"
    
    async def get_streamer_info(self, user_id: str) -> Optional[UnifiedStreamer]:
        """获取主播详细信息 - 默认实现"""
        self.logger.info(f"获取主播信息", user_id=user_id)
        
        # 默认实现：从直播列表中查找
        streamers = await self.get_live_list(size=100)
        for streamer in streamers:
            if streamer.platform_user_id == user_id:
                return streamer
        
        self.logger.warning(f"未找到主播", user_id=user_id)
        return None
    
    async def batch_check_status(self, user_ids: List[str]) -> Dict[str, bool]:
        """批量检查主播状态 - 默认实现"""
        self.logger.info(f"批量检查主播状态", count=len(user_ids))
        
        streamers = await self.get_live_list(size=100)
        live_ids = {s.platform_user_id for s in streamers if s.is_live}
        
        return {uid: uid in live_ids for uid in user_ids}
    
    async def close(self):
        """关闭适配器，释放资源"""
        if self.http_client:
            await self.http_client.close()
            self.http_client = None
        self.logger.info("适配器已关闭")
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close() 