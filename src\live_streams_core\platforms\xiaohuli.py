"""
小狐狸平台适配器

小狐狸平台需要JWT认证，部分响应数据使用AES-CBC加密
"""

import json
from datetime import datetime
from typing import Any, Dict, List, Optional

from loguru import logger

from ..core.base import PlatformAdapter
from ..core.exceptions import AuthenticationError, DecryptError, PlatformException
from ..core.models import Platform, StreamQuality, StreamType, UnifiedStreamer
from ..utils.crypto import CryptoUtils


class XiaohuliAdapter(PlatformAdapter):
    """小狐狸平台适配器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化适配器
        
        Args:
            config: 配置信息，包含以下参数:
                认证参数（必需）:
                - jwt_token: JWT令牌（必需，通过登录API获取）
                - token: 解密token（必需，用于房间信息解密）
                - x_live_butter2: 加密参数（可选，某些API可能需要）
                
                网络参数（可选）:
                - base_url: 自定义API域名（可选，默认: https://api.moonscap.com）
                
        注意：
            - JWT token有效期30天，过期需要重新登录获取
            - 登录API不需要认证，其他API都需要
            - 调用者负责管理和更新认证信息
            - 如果平台域名变更，可通过base_url参数指定新域名
        """
        # 先设置认证信息，因为_build_headers会在super().__init__中被调用
        self.config = config or {}
        self.jwt_token = self.config.get("jwt_token", "")
        self.decrypt_token = self.config.get("token", "")
        self.x_live_butter2 = self.config.get("x_live_butter2", "")
        
        # 检查是否已认证（非登录API需要）
        self.is_authenticated = bool(self.jwt_token and self.decrypt_token)
        
        # 调用父类初始化
        super().__init__(config)
        
        # 如果提供了认证信息，立即更新请求头
        if self.is_authenticated and self.http_client:
            self._update_auth_headers()
    
    def _get_platform(self) -> Platform:
        """获取平台标识"""
        return Platform.XIAOHULI
    
    def _get_base_url(self) -> str:
        """获取基础URL"""
        # 支持自定义域名，如果配置中有base_url则使用，否则使用默认值
        return self.config.get("base_url", "https://api.moonscap.com")
    
    def _build_headers(self) -> Dict[str, str]:
        """构建请求头"""
        headers = {
            "Host": "api.moonscap.com",
            "accept-encoding": "br,gzip",
            "user-agent": "okhttp/4.9.3",
            "x-accept-puzzle": "cola,tiger,tiger2,panda",
            "knockknock": "synergy",
            "x-live-pretty": "spring"
        }
        
        # 如果有JWT令牌，添加认证头
        if self.jwt_token:
            headers["authorization"] = f"Bearer {self.jwt_token}"
        
        # 如果有x-live-butter2参数，添加到请求头
        if self.x_live_butter2:
            headers["x-live-butter2"] = self.x_live_butter2
        
        return headers
    
    def _update_auth_headers(self):
        """更新认证相关的请求头"""
        if self.http_client and self.http_client.client:
            if self.jwt_token:
                self.http_client.client.headers["authorization"] = f"Bearer {self.jwt_token}"
            if self.x_live_butter2:
                self.http_client.client.headers["x-live-butter2"] = self.x_live_butter2
    
    def update_auth(self, jwt_token: str, token: str, x_live_butter2: Optional[str] = None):
        """
        更新认证信息
        
        Args:
            jwt_token: 新的JWT令牌
            token: 新的解密token
            x_live_butter2: 新的加密参数（可选）
        """
        self.jwt_token = jwt_token
        self.decrypt_token = token
        if x_live_butter2 is not None:
            self.x_live_butter2 = x_live_butter2
        
        self.is_authenticated = bool(self.jwt_token and self.decrypt_token)
        self._update_auth_headers()
        
        self.logger.info("认证信息已更新")
    

    
    def _check_authentication(self):
        """检查是否已认证"""
        if not self.is_authenticated:
            raise AuthenticationError(
                "未提供认证信息。请先通过登录API获取jwt_token和token，"
                "然后在创建适配器时传入config参数"
            )
    
    # ========== 统一接口实现 ==========
    
    async def get_live_list(
        self,
        category: str = "hot",
        page: int = 1,
        size: int = 20,
        **kwargs
    ) -> List[UnifiedStreamer]:
        """获取直播列表 - 只返回统一格式"""
        response = await self.get_live_list_with_raw(
            category=category,
            page=page,
            size=size,
            **kwargs
        )
        return response.items
    
    async def _fetch_live_list_raw(
        self,
        category: str,
        page: int,
        size: int,
        **kwargs
    ) -> Dict[str, Any]:
        """获取直播列表的原始响应"""
        self._check_authentication()
        
        # 小狐狸的分类映射
        category_map = {
            "hot": "hot",          # 热门
            "follow": "onlineFriends",  # 关注
            "vip": "vip",          # 收费
            "all": "all",          # 全部
            "vegan": "vegan",      # 颜值
            "nearby": "nearby"     # 附近
        }
        
        xiaohuli_category = category_map.get(category, "hot")
        
        # 构建URL
        url = f"/OpenAPI/v1/anchor/{xiaohuli_category}"
        
        # 关注列表不需要分页参数
        if xiaohuli_category == "onlineFriends":
            params = {}
        else:
            params = {
                "page": page,
                "size": size
            }
        
        # 发送请求
        response = await self.raw_request("GET", url, params=params)
        
        if response.get("code") != 0:
            raise PlatformException(f"获取直播列表失败: {response.get('msg')}")
        
        return response
    
    def _extract_items_from_response(self, response: Dict[str, Any]) -> List[Dict[str, Any]]:
        """从响应中提取数据项列表"""
        data = response.get("data", {})
        
        # 关注列表的数据直接在data数组中
        if isinstance(data, list):
            return data
        
        # 其他列表的数据在data.list中
        if isinstance(data, dict) and "list" in data:
            return data["list"]
        
        self.logger.warning("无法从响应中提取数据列表", response_keys=list(response.keys()))
        return []
    
    def _extract_total_from_response(self, response: Dict[str, Any]) -> Optional[int]:
        """从响应中提取总数"""
        data = response.get("data", {})
        
        # 关注列表没有总数
        if isinstance(data, list):
            return len(data)
        
        # 其他列表的总数在data.total_cnt中
        if isinstance(data, dict):
            return data.get("total_cnt") or data.get("row_count")
        
        return None
    
    def parse_streamer(self, raw_data: Dict[str, Any]) -> UnifiedStreamer:
        """解析原始数据为统一格式"""
        # 提取关键字段
        user_id = str(raw_data.get("id", ""))
        room_num = str(raw_data.get("curroomnum", user_id))
        
        # 解析直播状态
        is_live = raw_data.get("broadcasting") == "y"
        
        # 解析观看人数
        viewer_count = raw_data.get("online", 0)
        
        # 解析房间类型
        limit_info = raw_data.get("limit", {})
        room_type = int(limit_info.get("ptid", "0"))
        
        # 判断是否需要特殊权限
        need_password = room_type == 1  # 密码房
        need_payment = room_type == 2   # 收费房
        need_level = room_type == 3     # 等级房
        is_private = room_type == 6     # 私密团
        
        # 解析开播时间
        start_time = None
        if "starttime" in raw_data:
            try:
                start_time = datetime.fromtimestamp(int(raw_data["starttime"]))
            except (ValueError, TypeError):
                pass
        
        # 构建头像和封面URL
        avatar_url = raw_data.get("avatar", "")
        snap_url = raw_data.get("snap", "")
        
        # 补全URL
        if avatar_url and not avatar_url.startswith("http"):
            avatar_url = f"https://static.moonscap.com{avatar_url}"
        if snap_url and not snap_url.startswith("http"):
            snap_url = f"https://static.moonscap.com{snap_url}"
        
        # 解析标签
        tags = []
        if raw_data.get("toy_status") == 1:
            tags.append("玩具")
        if raw_data.get("private") == 1:
            tags.append("私密")
        
        # 构建统一数据模型
        streamer = UnifiedStreamer(
            id=self._build_unified_id(user_id),
            platform=self.platform,
            platform_user_id=user_id,
            platform_room_id=room_num,
            username=user_id,  # 小狐狸没有单独的用户名字段
            nickname=raw_data.get("nickname", ""),
            avatar_url=avatar_url,
            cover_url=snap_url,
            is_live=is_live,
            title=raw_data.get("roomTitle", raw_data.get("intro", "")),
            viewer_count=viewer_count,
            start_time=start_time,
            category=str(raw_data.get("channel_id", "")),
            tags=tags,
            stream_qualities=[],  # 流地址需要单独获取
            is_vip=need_payment or is_private,
            need_password=need_password,
            need_login=True,  # 小狐狸平台都需要登录
            raw_data=raw_data
        )
        
        # 添加平台特定字段
        streamer.extra_fields.update({
            "room_type": room_type,
            "room_type_name": self._get_room_type_name(room_type),
            "prerequisite": limit_info.get("prerequisite", 0),  # 收费金币数
            "city": raw_data.get("city", ""),
            "province": raw_data.get("province", ""),
            "sex": raw_data.get("sex", 0),
            "intro": raw_data.get("intro", ""),
            "is_attention": raw_data.get("is_attention", 0),
            "toy_status": raw_data.get("toy_status", 0),
            "video_status": raw_data.get("video_status", 0)
        })
        
        return streamer
    
    def _get_room_type_name(self, room_type: int) -> str:
        """获取房间类型名称"""
        type_map = {
            0: "公开房",
            1: "密码房",
            2: "收费房",
            3: "等级房",
            4: "计时房",
            6: "私密团"
        }
        return type_map.get(room_type, "未知")
    
    async def get_stream_url(self, room_id: str) -> List[StreamQuality]:
        """获取直播流地址"""
        self._check_authentication()
        
        # 调用房间信息API
        response = await self.raw_request(
            "GET",
            "/OpenAPI/v1/private/getPrivateLimit",
            params={"uid": room_id}
        )
        
        if response.get("code") != 0:
            self.logger.error("获取房间信息失败", room_id=room_id, msg=response.get("msg"))
            return []
        
        # 解密响应数据
        encrypted_data = response.get("data", "")
        if not encrypted_data:
            self.logger.warning("房间信息响应数据为空", room_id=room_id)
            return []
        
        # 使用token的左16位和右16位作为密钥和IV
        if not self.decrypt_token or len(self.decrypt_token) < 32:
            raise DecryptError("解密token无效或未设置")
        
        key = self.decrypt_token[:16]
        iv = self.decrypt_token[16:32]
        
        try:
            decrypted_json = CryptoUtils.aes_cbc_decrypt(encrypted_data, key=key, iv=iv)
            room_data = json.loads(decrypted_json)
        except Exception as e:
            self.logger.error("解密房间信息失败", error=str(e))
            raise DecryptError(f"解密房间信息失败: {str(e)}")
        
        # 检查主播是否在线
        if room_data.get("online") != 1:
            self.logger.info("主播不在线", room_id=room_id)
            return []
        
        # 检查房间类型
        room_type = int(room_data.get("ptid", "0"))
        if room_type != 0:
            self.logger.info("非公开房间，无法获取流地址", room_id=room_id, room_type=room_type)
            return []
        
        # 提取流地址
        stream_data = room_data.get("stream", {})
        if not stream_data:
            self.logger.warning("无流地址数据", room_id=room_id)
            return []
        
        stream_qualities = []
        
        # RTMP流
        if stream_data.get("pull_url"):
            stream_qualities.append(
                StreamQuality(
                    quality="origin",
                    url=stream_data["pull_url"],
                    format=StreamType.RTMP
                )
            )
        
        # HTTP-FLV流
        if stream_data.get("flv_pull_url"):
            stream_qualities.append(
                StreamQuality(
                    quality="origin",
                    url=stream_data["flv_pull_url"],
                    format=StreamType.FLV
                )
            )
        
        # WebRTC流
        if stream_data.get("lll_pull_url"):
            stream_qualities.append(
                StreamQuality(
                    quality="origin",
                    url=stream_data["lll_pull_url"],
                    format=StreamType.WEBRTC
                )
            )
        
        return stream_qualities
    
    # ========== 小狐狸平台特定方法 ==========
    
    async def get_app_config(self, app_ver: str = "1.0.0") -> Dict[str, Any]:
        """
        获取应用配置 - 小狐狸平台特定功能
        
        Args:
            app_ver: 应用版本号
            
        Returns:
            应用配置信息
        """
        # 获取应用配置不需要认证
        response = await self.raw_request(
            "GET",
            "/OpenAPI/v1/config/getappconfig",
            params={"app_ver": app_ver}
        )
        
        if response.get("code") != 0:
            raise PlatformException(f"获取应用配置失败: {response.get('msg')}")
        
        return response.get("data", {})
    
    async def follow_streamer(self, user_id: str, room_id: Optional[str] = None) -> bool:
        """
        关注主播 - 小狐狸平台特定功能
        
        Args:
            user_id: 主播ID
            room_id: 房间号（可选）
            
        Returns:
            是否成功
        """
        self._check_authentication()
        
        params = {"uid": user_id}
        if room_id:
            params["roomid"] = room_id
        
        response = await self.raw_request("GET", "/OpenAPI/v1/user/follow", params=params)
        
        return response.get("code") == 0 and response.get("data") == "_ATTENTION_SUCCESS_"
    
    async def unfollow_streamer(self, user_id: str, room_id: Optional[str] = None) -> bool:
        """
        取消关注主播 - 小狐狸平台特定功能
        
        Args:
            user_id: 主播ID
            room_id: 房间号（可选）
            
        Returns:
            是否成功
        """
        self._check_authentication()
        
        params = {"uid": user_id}
        if room_id:
            params["roomid"] = room_id
        
        response = await self.raw_request("GET", "/OpenAPI/v1/user/unfollow", params=params)
        
        return response.get("code") == 0 and response.get("data") == "_ATTENTION_CANCELED_"
    
    async def get_ranking_list(
        self,
        ranking_type: str = "today",
        page_index: int = 1,
        page_size: int = 30
    ) -> List[Dict[str, Any]]:
        """
        获取排行榜 - 小狐狸平台特定功能
        
        Args:
            ranking_type: 排行榜类型（today/yesterday/week）
            page_index: 页码
            page_size: 每页数量
            
        Returns:
            排行榜数据
        """
        self._check_authentication()
        
        # 排行榜类型映射
        type_map = {
            "today": "anchorRankToday",
            "yesterday": "anchorRankYesterday",
            "week": "anchorRankLastWeek"
        }
        
        rank_endpoint = type_map.get(ranking_type, "anchorRankToday")
        
        response = await self.raw_request(
            "GET",
            f"/OpenAPI/v1/rank/{rank_endpoint}",
            params={
                "page_index": page_index,
                "page_size": page_size
            }
        )
        
        if response.get("code") != 0:
            raise PlatformException(f"获取排行榜失败: {response.get('msg')}")
        
        return response.get("data", {}).get("list", [])
    
    async def get_user_profile(self, user_id: str) -> Dict[str, Any]:
        """
        获取用户详细信息 - 小狐狸平台特定功能
        
        Args:
            user_id: 用户ID
            
        Returns:
            用户详细信息
        """
        self._check_authentication()
        
        response = await self.raw_request(
            "GET",
            "/OpenAPI/v1/user/profile",
            params={"uid": user_id}
        )
        
        if response.get("code") != 0:
            raise PlatformException(f"获取用户信息失败: {response.get('msg')}")
        
        return response.get("data", {})
    
    @staticmethod
    async def get_login_captcha(http_client=None) -> Dict[str, str]:
        """
        获取登录验证码 - 小狐狸平台特定功能
        
        Args:
            http_client: HTTP客户端（可选）
            
        Returns:
            包含captcha_id和image的字典
        """
        from ..utils.http_client import create_client
        
        client = http_client or create_client(
            base_url="https://api.moonscap.com",
            headers={
                "Host": "api.moonscap.com",
                "accept-encoding": "br,gzip",
                "user-agent": "okhttp/4.9.3"
            }
        )
        
        try:
            response = await client.get("/OpenAPI/v1/user/get_login_captcha")
            data = response.json()
            
            if data.get("code") != 0:
                raise AuthenticationError(f"获取验证码失败: {data.get('msg')}")
            
            result = data.get("data", {})
            return {
                "captcha_id": result.get("captcha_id", ""),
                "image": result.get("captcha_image", "")
            }
        finally:
            if not http_client:
                await client.close()
    
    @staticmethod
    async def login(
        username: str, 
        password: str, 
        captcha: str, 
        captcha_id: str,
        http_client=None
    ) -> Dict[str, Any]:
        """
        账号登录 - 小狐狸平台特定功能
        
        Args:
            username: 用户名
            password: 密码
            captcha: 验证码
            captcha_id: 验证码ID
            http_client: HTTP客户端（可选）
            
        Returns:
            登录响应数据，包含jwt_token和token
        """
        from ..utils.http_client import create_client
        
        client = http_client or create_client(
            base_url="https://api.moonscap.com",
            headers={
                "Host": "api.moonscap.com",
                "accept-encoding": "br,gzip",
                "user-agent": "okhttp/4.9.3"
            }
        )
        
        try:
            response = await client.post(
                "/OpenAPI/v1/user/login",
                json={
                    "username": username,
                    "password": password,
                    "captcha": captcha,
                    "captcha_id": captcha_id,
                    "country_code": "+86"
                }
            )
            
            data = response.json()
            if data.get("code") != 0:
                raise AuthenticationError(f"登录失败: {data.get('msg')}")
            
            # 解密响应数据
            encrypted_data = data.get("data", "")
            if not encrypted_data:
                raise DecryptError("登录响应数据为空")
            
            # 使用固定的AES密钥解密
            decrypted_json = CryptoUtils.aes_cbc_decrypt(
                encrypted_data,
                key="xW.uc8LUi.x7@k!p",
                iv="Nz_zu4*xT8-8Z4ve"
            )
            
            login_data = json.loads(decrypted_json)
            logger.info("登录成功", user_id=login_data.get("user", {}).get("id"))
            
            return login_data
            
        finally:
            if not http_client:
                await client.close()
    
    @staticmethod
    async def guest_login(device_name: str = "samsung-SM-N976N", http_client=None) -> Dict[str, Any]:
        """
        游客登录 - 小狐狸平台特定功能
        
        Args:
            device_name: 设备名称
            http_client: HTTP客户端（可选）
            
        Returns:
            登录响应数据，包含jwt_token和token
        """
        from ..utils.http_client import create_client
        
        client = http_client or create_client(
            base_url="https://api.moonscap.com",
            headers={
                "Host": "api.moonscap.com",
                "accept-encoding": "br,gzip",
                "user-agent": "okhttp/4.9.3"
            }
        )
        
        try:
            response = await client.post(
                "/OpenAPI/v2/guest/login",
                json={"device_name": device_name}
            )
            
            data = response.json()
            if data.get("code") != 0:
                raise AuthenticationError(f"游客登录失败: {data.get('msg')}")
            
            # 解密响应数据
            encrypted_data = data.get("data", "")
            if not encrypted_data:
                raise DecryptError("游客登录响应数据为空")
            
            # 使用固定的AES密钥解密
            decrypted_json = CryptoUtils.aes_cbc_decrypt(
                encrypted_data,
                key="xW.uc8LUi.x7@k!p",
                iv="Nz_zu4*xT8-8Z4ve"
            )
            
            login_data = json.loads(decrypted_json)
            logger.info("游客登录成功", user_id=login_data.get("user", {}).get("id"))
            
            return login_data
            
        finally:
            if not http_client:
                await client.close() 