[tool.poetry]
name = "live-streams-core"
version = "0.1.0"
description = "多平台直播聚合核心库"
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"
license = "MIT"
packages = [{include = "live_streams_core", from = "src"}]

[tool.poetry.dependencies]
python = "^3.8"
httpx = {extras = ["http2"], version = "^0.24.0"}
loguru = "^0.7.0"
pydantic = "^2.0.0"
orjson = "^3.9.0"
cryptography = "^41.0.0"
pycryptodome = "^3.18.0"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.0"
pytest-asyncio = "^0.21.0"
pytest-httpx = "^0.24.0"
black = "^23.7.0"
ruff = "^0.0.280"
mypy = "^1.4.0"
pre-commit = "^3.3.3"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 100
target-version = ['py38']

[tool.ruff]
line-length = 100
select = ["E", "F", "I", "N", "W", "B", "C90", "UP"]
ignore = ["E501"]  # 忽略行长度限制

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
check_untyped_defs = true
ignore_missing_imports = true

[tool.pytest.ini_options]
asyncio_mode = "auto"
testpaths = ["tests"] 