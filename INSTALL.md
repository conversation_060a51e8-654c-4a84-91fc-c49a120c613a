# Live Streams Core 安装指南

## 环境要求

- Python 3.8+
- Windows/Linux/macOS

## 安装步骤

### 1. 克隆或下载代码

```bash
cd /d:/xiangmu/live/live_streams_core
```

### 2. 创建虚拟环境（推荐）

```bash
# Windows
python -m venv venv
venv\Scripts\activate

# Linux/macOS
python -m venv venv
source venv/bin/activate
```

### 3. 安装依赖

如果你有Poetry：

```bash
poetry install
```

如果没有Poetry，使用pip：

```bash
# 首先安装基础依赖
pip install httpx[http2] loguru pydantic orjson cryptography pycryptodome

# 安装开发依赖（可选）
pip install pytest pytest-asyncio pytest-httpx black ruff mypy
```

### 4. 验证安装

运行示例代码验证安装是否成功：

```bash
cd examples
python basic_usage.py
```

## 使用方法

### 基本使用

```python
import asyncio
from live_streams_core.platforms.swag import SwagAdapter

async def main():
    # 创建适配器
    adapter = SwagAdapter()
    
    # 获取直播列表
    streamers = await adapter.get_live_list()
    
    for streamer in streamers:
        print(f"{streamer.nickname}: {streamer.title}")
    
    # 记得关闭适配器
    await adapter.__aexit__(None, None, None)

asyncio.run(main())
```

### 开发模式

如果要进行开发，建议使用开发模式安装：

```bash
# 从项目根目录
pip install -e .
```

这样你对代码的修改会立即生效，无需重新安装。

## 常见问题

### Q: 遇到 ImportError

A: 确保已经正确安装所有依赖，特别是 pycryptodome：

```bash
pip install pycryptodome
```

### Q: Windows上的事件循环错误

A: 在Windows上运行异步代码时，添加以下设置：

```python
import asyncio
import sys

if sys.platform == "win32":
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
```

### Q: 代理设置

A: 如果需要使用代理，在创建适配器时传入配置：

```python
adapter = SwagAdapter(config={
    "proxy": "http://your-proxy:port"
})
```

## 下一步

- 查看 [examples/](examples/) 目录了解更多使用示例
- 阅读 [DESIGN.md](DESIGN.md) 了解架构设计
- 参考 [DEVELOPMENT_FLOW.md](DEVELOPMENT_FLOW.md) 了解开发计划 