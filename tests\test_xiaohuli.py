"""
小狐狸平台适配器测试
"""

import json
import pytest
import pytest_asyncio
from unittest.mock import MagicMock, patch

import sys
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from live_streams_core import Platform, AuthenticationError, DecryptError
from live_streams_core.platforms.xiaohuli import XiaohuliAdapter


@pytest.fixture
def mock_hot_list_response():
    """模拟热门列表响应"""
    return {
        "code": 0,
        "error_code": 0,
        "msg": "操作成功",
        "data": {
            "list": [
                {
                    "id": "69459324",
                    "curroomnum": "1558369600",
                    "online": 11049,
                    "broadcasting": "y",
                    "roomTitle": "可约可1有福利",
                    "avatar": "/style/avatar/277/69459324_middle.jpg?t=1747464930",
                    "snap": "/style/avatar/277/69459324.jpg?t=1747464930",
                    "province": "保密",
                    "city": "MARS",
                    "nickname": "上官芷",
                    "limit": {
                        "ptid": "0",  # 公开房
                        "prerequisite": 0,
                        "bsid": "6092392",
                        "sid": "48"
                    },
                    "toy_status": 1,
                    "private": 0,
                    "video_status": 0,
                    "starttime": "1747648575",
                    "is_attention": 0
                },
                {
                    "id": "12345678",
                    "curroomnum": "987654321",
                    "online": 5000,
                    "broadcasting": "y",
                    "roomTitle": "收费房间测试",
                    "nickname": "付费主播",
                    "limit": {
                        "ptid": "2",  # 收费房
                        "prerequisite": 100,
                    },
                    "toy_status": 0,
                    "private": 1
                }
            ],
            "row_count": 2,
            "total_cnt": 100,
            "page_index": 1,
            "page_size": 50,
            "page_count": 2
        }
    }


@pytest.fixture
def mock_follow_list_response():
    """模拟关注列表响应（数据直接在data数组中）"""
    return {
        "code": 0,
        "error_code": 0,
        "msg": "操作成功",
        "data": [
            {
                "id": "39545012",
                "curroomnum": "1391966453",
                "broadcasting": "y",
                "online": 9398,
                "avatar": "/style/avatar/362/39545012_middle.jpg?t=1729962651",
                "snap": "/style/avatar/362/39545012.jpg?t=1729962651",
                "city": "杭州市",
                "nickname": "别叫牛奶",
                "private": 1,
                "starttime": "1747665649",
                "limit": {
                    "ptid": "0",
                    "prerequisite": 0,
                },
                "toy_status": 0,
                "intro": "❤️这世上总会有人，因为你是你而爱你❤️",
                "sex": 1
            }
        ]
    }


@pytest.fixture
def mock_room_info_encrypted():
    """模拟加密的房间信息响应"""
    # 这是一个模拟的加密数据，实际使用时需要真实的加密数据
    return {
        "code": 0,
        "error_code": 0,
        "msg": "操作成功",
        "data": "base64_encrypted_data_here"
    }


@pytest.fixture
def mock_login_response_encrypted():
    """模拟加密的登录响应"""
    return {
        "code": 0,
        "error_code": 0,
        "msg": "操作成功",
        "data": "base64_encrypted_login_data"
    }


@pytest.mark.asyncio
async def test_xiaohuli_adapter_init():
    """测试适配器初始化"""
    # 不带配置初始化
    adapter = XiaohuliAdapter()
    assert adapter.platform == Platform.XIAOHULI
    assert adapter.base_url == "https://api.moonscap.com"
    assert not adapter.is_authenticated
    
    # 带JWT令牌初始化
    adapter_with_token = XiaohuliAdapter(config={
        "jwt_token": "test_jwt_token",
        "token": "test_decrypt_token"
    })
    assert adapter_with_token.is_authenticated
    assert adapter_with_token.jwt_token == "test_jwt_token"


@pytest.mark.asyncio
async def test_parse_streamer(mock_hot_list_response):
    """测试解析主播数据"""
    adapter = XiaohuliAdapter()
    
    # 测试公开房间
    raw_data = mock_hot_list_response["data"]["list"][0]
    streamer = adapter.parse_streamer(raw_data)
    
    assert streamer.id == "xiaohuli_69459324"
    assert streamer.platform == Platform.XIAOHULI
    assert streamer.platform_user_id == "69459324"
    assert streamer.platform_room_id == "1558369600"
    assert streamer.nickname == "上官芷"
    assert streamer.is_live is True
    assert streamer.viewer_count == 11049
    assert streamer.title == "可约可1有福利"
    assert not streamer.need_password
    assert not streamer.is_vip
    assert streamer.extra_fields["room_type"] == 0
    assert streamer.extra_fields["room_type_name"] == "公开房"
    
    # 测试收费房间
    raw_data2 = mock_hot_list_response["data"]["list"][1]
    streamer2 = adapter.parse_streamer(raw_data2)
    
    assert streamer2.extra_fields["room_type"] == 2
    assert streamer2.extra_fields["room_type_name"] == "收费房"
    assert streamer2.extra_fields["prerequisite"] == 100
    assert streamer2.is_vip is True


@pytest.mark.asyncio
async def test_extract_items_from_response(mock_hot_list_response, mock_follow_list_response):
    """测试从响应中提取数据"""
    adapter = XiaohuliAdapter()
    
    # 测试普通列表（data.list）
    items = adapter._extract_items_from_response(mock_hot_list_response)
    assert len(items) == 2
    assert items[0]["id"] == "69459324"
    
    # 测试关注列表（data数组）
    follow_items = adapter._extract_items_from_response(mock_follow_list_response)
    assert len(follow_items) == 1
    assert follow_items[0]["id"] == "39545012"


@pytest.mark.asyncio
async def test_get_room_type_name():
    """测试房间类型名称获取"""
    adapter = XiaohuliAdapter()
    
    assert adapter._get_room_type_name(0) == "公开房"
    assert adapter._get_room_type_name(1) == "密码房"
    assert adapter._get_room_type_name(2) == "收费房"
    assert adapter._get_room_type_name(3) == "等级房"
    assert adapter._get_room_type_name(4) == "计时房"
    assert adapter._get_room_type_name(6) == "私密团"
    assert adapter._get_room_type_name(99) == "未知"


@pytest.mark.asyncio
async def test_check_authentication():
    """测试认证检查"""
    adapter = XiaohuliAdapter()
    
    # 未认证时应该抛出异常
    with pytest.raises(AuthenticationError):
        adapter._check_authentication()
    
    # 设置认证后不应该抛出异常
    adapter.is_authenticated = True
    adapter._check_authentication()  # 不应该抛出异常


@pytest.mark.asyncio
async def test_guest_login_decrypt(httpx_mock):
    """测试游客登录解密"""
    adapter = XiaohuliAdapter()
    
    # 模拟加密的登录数据
    login_data = {
        "jwt_token": "test_jwt_token",
        "token": "test_decrypt_token_32_chars_long",
        "user": {
            "id": "69509880",
            "nickname": "游客69509880",
            "guest": True
        }
    }
    
    # 使用真实的加密函数加密数据
    from live_streams_core.utils.crypto import CryptoUtils
    encrypted_data = CryptoUtils.aes_cbc_encrypt(
        json.dumps(login_data),
        key="xW.uc8LUi.x7@k!p",
        iv="Nz_zu4*xT8-8Z4ve"
    )
    
    httpx_mock.add_response(
        url="https://api.moonscap.com/OpenAPI/v2/guest/login",
        json={
            "code": 0,
            "error_code": 0,
            "msg": "操作成功",
            "data": encrypted_data
        }
    )
    
    # 执行游客登录
    result = await adapter.guest_login("test-device")
    
    assert result["jwt_token"] == "test_jwt_token"
    assert result["user"]["id"] == "69509880"
    assert adapter.is_authenticated
    assert adapter.jwt_token == "test_jwt_token"


@pytest.mark.asyncio
async def test_get_stream_url_decrypt():
    """测试获取流地址解密"""
    adapter = XiaohuliAdapter(config={
        "jwt_token": "test_jwt",
        "token": "eca47b4ab3f300b3038f4e7be0371518"  # 32字符的token
    })
    adapter.is_authenticated = True
    
    # 模拟解密后的房间数据
    room_data = {
        "online": 1,
        "ptid": "0",
        "stream": {
            "pull_url": "rtmp://example.com/live/stream",
            "flv_pull_url": "http://example.com/live/stream.flv",
            "lll_pull_url": "webrtc://example.com/live/stream"
        }
    }
    
    # 使用token的前16位和后16位作为key和iv
    from live_streams_core.utils.crypto import CryptoUtils
    encrypted_data = CryptoUtils.aes_cbc_encrypt(
        json.dumps(room_data),
        key="eca47b4ab3f300b3",
        iv="038f4e7be0371518"
    )
    
    # Mock HTTP请求
    with patch.object(adapter, 'raw_request') as mock_request:
        mock_request.return_value = {
            "code": 0,
            "error_code": 0,
            "msg": "操作成功",
            "data": encrypted_data
        }
        
        # 获取流地址
        streams = await adapter.get_stream_url("123456")
        
        assert len(streams) == 3
        assert any(s.format.value == "rtmp" for s in streams)
        assert any(s.format.value == "flv" for s in streams)
        assert any(s.format.value == "webrtc" for s in streams)


@pytest.mark.asyncio
async def test_platform_specific_methods():
    """测试平台特定方法"""
    adapter = XiaohuliAdapter(config={"jwt_token": "test_jwt"})
    adapter.is_authenticated = True
    
    # 测试关注功能
    with patch.object(adapter, 'raw_request') as mock_request:
        mock_request.return_value = {
            "code": 0,
            "data": "_ATTENTION_SUCCESS_"
        }
        
        result = await adapter.follow_streamer("123456")
        assert result is True
        
    # 测试取消关注
    with patch.object(adapter, 'raw_request') as mock_request:
        mock_request.return_value = {
            "code": 0,
            "data": "_ATTENTION_CANCELED_"
        }
        
        result = await adapter.unfollow_streamer("123456")
        assert result is True
        
    # 测试获取排行榜
    with patch.object(adapter, 'raw_request') as mock_request:
        mock_request.return_value = {
            "code": 0,
            "data": {
                "list": [
                    {"user_id": "1", "nickname": "主播1", "coin": 1000},
                    {"user_id": "2", "nickname": "主播2", "coin": 800}
                ]
            }
        }
        
        ranking = await adapter.get_ranking_list("today")
        assert len(ranking) == 2
        assert ranking[0]["coin"] == 1000


if __name__ == "__main__":
    pytest.main([__file__, "-v"]) 