"""
绿茶平台适配器

绿茶平台特点：
- 需要token认证
- FLV流地址使用AES-ECB加密
- 简单的JSON API
"""

from datetime import datetime
from typing import Any, Dict, List, Optional

from loguru import logger

from ..core.base import PlatformAdapter
from ..core.exceptions import AuthenticationError, PlatformException
from ..core.models import Platform, StreamQuality, StreamType, UnifiedStreamer
from ..utils.crypto import CryptoUtils


class LvchaAdapter(PlatformAdapter):
    """绿茶平台适配器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化适配器
        
        Args:
            config: 配置信息，包含以下参数:
                认证参数（必需）:
                - token: 认证token（必需）
                
                网络参数（可选）:
                - base_url: 自定义API域名（可选，默认: http://serone.ser77api.com）
        """
        # 先设置token，因为_build_headers会在super().__init__中被调用
        self.config = config or {}
        self.token = self.config.get("token", "")
        self.is_authenticated = bool(self.token)
        
        # 调用父类初始化
        super().__init__(config)
    
    def _get_platform(self) -> Platform:
        """获取平台标识"""
        return Platform.LVCHA
    
    def _get_base_url(self) -> str:
        """获取基础URL"""
        # 支持自定义域名，如果配置中有base_url则使用，否则使用默认值
        return self.config.get("base_url", "http://serone.ser77api.com")
    
    def _build_headers(self) -> Dict[str, str]:
        """构建请求头"""
        headers = {
            "version": "********",
            "Content-Type": "application/json;charset=UTF-8",
            "User-Agent": "okhttp/3.14.9",
            "Accept-Encoding": "gzip, deflate, br"
        }
        
        # 如果有token，添加到请求头
        if self.token:
            headers["token"] = self.token
        
        return headers
    
    def _check_authentication(self):
        """检查是否已认证"""
        if not self.is_authenticated:
            raise AuthenticationError(
                "未提供认证信息。请在创建适配器时传入token参数"
            )
    
    def _decrypt_flv_url(self, encrypted_url: str) -> str:
        """解密FLV流地址"""
        key = b'qwertyui12345678'
        try:
            return CryptoUtils.aes_ecb_decrypt(encrypted_url, key)
        except Exception as e:
            self.logger.error("解密流地址失败", error=str(e))
            return ""
    
    # ========== 统一接口实现 ==========
    
    async def get_live_list(
        self,
        category: str = "hot",
        page: int = 1,
        size: int = 20,
        **kwargs
    ) -> List[UnifiedStreamer]:
        """获取直播列表 - 只返回统一格式"""
        response = await self.get_live_list_with_raw(
            category=category,
            page=page,
            size=size,
            **kwargs
        )
        return response.items
    
    async def _fetch_live_list_raw(
        self,
        category: str,
        page: int,
        size: int,
        **kwargs
    ) -> Dict[str, Any]:
        """获取直播列表的原始响应"""
        self._check_authentication()
        
        # 绿茶的分类映射
        category_map = {
            "hot": 1,         # 热门推荐
            "goddess": 2,     # 性感女神
            "beauty": 3,      # 颜值星秀
            "vip": 4,         # 收费秀场
            "lottery": 5,     # 彩票直播
            "dance": 6,       # 性感热舞
            "singer": 7,      # 灵魂歌手
            "outdoor": 8      # 激情户外
        }
        
        cate = category_map.get(category, 1)
        
        # 构建请求参数
        params = {
            "cate": cate,
            "condition": "",
            "filmSupport": 1,
            "page_num": page,
            "page_size": size
        }
        
        # 发送请求
        response = await self.raw_request(
            "POST",
            "/live-ns/index/getVideoList",
            json=params
        )
        
        if response.get("code") != 0:
            raise PlatformException(f"获取直播列表失败: {response.get('msg')}")
        
        return response
    
    def _extract_items_from_response(self, response: Dict[str, Any]) -> List[Dict[str, Any]]:
        """从响应中提取数据项列表"""
        data = response.get("data", {})
        if isinstance(data, dict) and "data" in data:
            return data["data"]
        
        self.logger.warning("无法从响应中提取数据列表", response_keys=list(response.keys()))
        return []
    
    def _extract_total_from_response(self, response: Dict[str, Any]) -> Optional[int]:
        """从响应中提取总数"""
        data = response.get("data", {})
        if isinstance(data, dict):
            return data.get("count", 0)
        return None
    
    def parse_streamer(self, raw_data: Dict[str, Any]) -> UnifiedStreamer:
        """解析原始数据为统一格式"""
        # 提取关键字段
        user_id = str(raw_data.get("userId", ""))
        room_id = str(raw_data.get("id", ""))
        
        # 解析直播状态
        is_live = raw_data.get("liveIn") == "1"
        
        # 解析观看人数
        viewer_count = int(raw_data.get("watchNumber", "0"))
        
        # 解析付费信息
        is_live_pay = raw_data.get("isLivePay") == "1"
        live_fee = int(raw_data.get("liveFee", "0"))
        is_vip = is_live_pay and live_fee > 0
        
        # 构建头像和封面URL
        live_image = raw_data.get("liveImage", "")
        
        # 解析标签
        tags = []
        if raw_data.get("createType"):
            tags.append("创建类型")
        if raw_data.get("concatVideo"):
            tags.append("连接视频")
        
        # 添加彩票相关标签
        if raw_data.get("lotteryName"):
            tags.append(f"彩票: {raw_data['lotteryName']}")
        
        # 构建统一数据模型
        streamer = UnifiedStreamer(
            id=self._build_unified_id(user_id),
            platform=self.platform,
            platform_user_id=user_id,
            platform_room_id=room_id,
            username=user_id,
            nickname=raw_data.get("hostName", ""),
            avatar_url=live_image,
            cover_url=live_image,
            is_live=is_live,
            title=raw_data.get("title", ""),
            viewer_count=viewer_count,
            start_time=None,  # 绿茶API不提供开播时间
            category="",
            tags=tags,
            stream_qualities=[],  # 流地址需要单独获取
            is_vip=is_vip,
            need_password=False,
            need_login=True,  # 绿茶平台需要认证
            raw_data=raw_data
        )
        
        # 添加平台特定字段
        streamer.extra_fields.update({
            "group_id": raw_data.get("groupId", ""),
            "live_fee": live_fee,
            "lottery_id": raw_data.get("lotteryId", ""),
            "lottery_name": raw_data.get("lotteryName", ""),
            "video_type": raw_data.get("videoType", 0),
            "concat_video": raw_data.get("concatVideo", 0),
            "create_type": raw_data.get("createType", False),
            "encrypted_flv_url": raw_data.get("nplayFlv", "")
        })
        
        return streamer
    
    async def get_stream_url(self, room_id: str) -> List[StreamQuality]:
        """获取直播流地址"""
        self._check_authentication()
        
        try:
            # 获取房间详细信息
            response = await self.raw_request(
                "POST",
                "/live-ns/index/user_video_async",
                json={"room_id": room_id}
            )
            
            if response.get("code") != 0:
                self.logger.error("获取房间信息失败", room_id=room_id, msg=response.get("msg"))
                return []
            
            data = response.get("data", {})
            encrypted_flv = data.get("nplayFlv", "")
            
            stream_qualities = []
            
            if encrypted_flv:
                # 解密FLV流地址
                decrypted_url = self._decrypt_flv_url(encrypted_flv)
                if decrypted_url:
                    stream_qualities.append(
                        StreamQuality(
                            quality="origin",
                            url=decrypted_url,
                            format=StreamType.FLV
                        )
                    )
            
            return stream_qualities
            
        except Exception as e:
            self.logger.error("获取流地址失败", room_id=room_id, error=str(e))
            return []
    
    # ========== 绿茶平台特定方法 ==========
    
    async def get_room_detail(self, room_id: str) -> Dict[str, Any]:
        """
        获取房间详细信息 - 绿茶平台特定功能
        
        Args:
            room_id: 房间ID
            
        Returns:
            房间详细信息，包括观众列表、守护者信息等
        """
        self._check_authentication()
        
        response = await self.raw_request(
            "POST",
            "/live-ns/index/user_video_async",
            json={"room_id": room_id}
        )
        
        if response.get("code") != 0:
            raise PlatformException(f"获取房间详情失败: {response.get('msg')}")
        
        return response.get("data", {})
    
    async def get_viewer_list(self, room_id: str) -> List[Dict[str, Any]]:
        """
        获取观众列表 - 绿茶平台特定功能
        
        Args:
            room_id: 房间ID
            
        Returns:
            观众列表
        """
        room_detail = await self.get_room_detail(room_id)
        viewer_info = room_detail.get("viewer", {})
        return viewer_info.get("list", [])
    
    async def get_lottery_info(self, room_id: str) -> List[Dict[str, Any]]:
        """
        获取彩票信息 - 绿茶平台特定功能
        
        Args:
            room_id: 房间ID
            
        Returns:
            彩票插件信息列表
        """
        room_detail = await self.get_room_detail(room_id)
        return room_detail.get("h5Plugins", [])
    
    def update_auth(self, token: str):
        """
        更新认证信息
        
        Args:
            token: 新的认证token
        """
        self.token = token
        self.is_authenticated = bool(self.token)
        
        # 更新请求头
        if self.http_client and self.http_client.client:
            self.http_client.client.headers["token"] = self.token
        
        self.logger.info("认证信息已更新") 