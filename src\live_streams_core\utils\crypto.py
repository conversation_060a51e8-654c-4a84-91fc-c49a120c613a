"""
直播聚合核心库 - 加密解密工具

提供各平台所需的加密解密功能
"""

import base64
import hashlib
from typing import Union

from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad
from loguru import logger

from ..core.exceptions import DecryptError


class CryptoUtils:
    """加密解密工具集"""
    
    @staticmethod
    def xor_encrypt_decrypt(text: str, key: int = 5422) -> str:
        """
        XOR异或加密/解密（番茄平台使用）
        
        Args:
            text: 要加密/解密的文本
            key: 异或密钥，默认5422
            
        Returns:
            加密/解密后的文本
        """
        result = []
        for char in text:
            try:
                # XOR运算
                result.append(chr(ord(char) ^ key))
            except Exception:
                # 处理无法编码的字符
                result.append('\uFFFD')  # Unicode替换字符
        
        encrypted = ''.join(result)
        logger.debug(f"XOR加密/解密完成", input_len=len(text), output_len=len(encrypted))
        return encrypted
    
    @staticmethod
    def aes_ecb_encrypt(data: Union[str, bytes], key: Union[str, bytes]) -> str:
        """
        AES-ECB加密
        
        Args:
            data: 要加密的数据
            key: 密钥
            
        Returns:
            Base64编码的加密数据
        """
        try:
            # 确保key是bytes类型
            if isinstance(key, str):
                key = key.encode('utf-8')
            
            # 确保data是bytes类型
            if isinstance(data, str):
                data = data.encode('utf-8')
            
            # 创建加密器
            cipher = AES.new(key, AES.MODE_ECB)
            
            # 填充数据
            padded_data = pad(data, AES.block_size)
            
            # 加密
            encrypted = cipher.encrypt(padded_data)
            
            # Base64编码
            result = base64.b64encode(encrypted).decode('utf-8')
            
            logger.debug(f"AES-ECB加密完成", data_len=len(data), encrypted_len=len(result))
            return result
            
        except Exception as e:
            logger.error(f"AES-ECB加密失败", error=str(e))
            raise DecryptError(f"AES-ECB加密失败: {str(e)}")
    
    @staticmethod
    def aes_ecb_decrypt(encrypted_data: str, key: bytes) -> str:
        """
        AES-ECB解密
        
        Args:
            encrypted_data: Base64编码的加密数据
            key: 解密密钥
            
        Returns:
            解密后的字符串
        """
        try:
            from Crypto.Cipher import AES
            from Crypto.Util.Padding import unpad
            import base64
            
            # Base64解码
            encrypted_bytes = base64.b64decode(encrypted_data)
            
            # 创建AES解密器
            cipher = AES.new(key, AES.MODE_ECB)
            
            # 解密
            decrypted_bytes = cipher.decrypt(encrypted_bytes)
            
            # 去除填充
            decrypted_bytes = unpad(decrypted_bytes, AES.block_size)
            
            return decrypted_bytes.decode('utf-8')
            
        except Exception as e:
            logger.error("AES-ECB解密失败", error=str(e))
            raise
    
    @staticmethod
    def aes_cbc_encrypt(
        data: Union[str, bytes],
        key: Union[str, bytes],
        iv: Union[str, bytes]
    ) -> str:
        """
        AES-CBC加密
        
        Args:
            data: 要加密的数据
            key: 密钥
            iv: 初始化向量
            
        Returns:
            Base64编码的加密数据
        """
        try:
            # 确保参数是bytes类型
            if isinstance(key, str):
                key = key.encode('utf-8')
            if isinstance(iv, str):
                iv = iv.encode('utf-8')
            if isinstance(data, str):
                data = data.encode('utf-8')
            
            # 创建加密器
            cipher = AES.new(key, AES.MODE_CBC, iv)
            
            # 填充数据
            padded_data = pad(data, AES.block_size)
            
            # 加密
            encrypted = cipher.encrypt(padded_data)
            
            # Base64编码
            result = base64.b64encode(encrypted).decode('utf-8')
            
            logger.debug(f"AES-CBC加密完成", data_len=len(data), encrypted_len=len(result))
            return result
            
        except Exception as e:
            logger.error(f"AES-CBC加密失败", error=str(e))
            raise DecryptError(f"AES-CBC加密失败: {str(e)}")
    
    @staticmethod
    def aes_cbc_decrypt(
        encrypted_data: Union[str, bytes],
        key: Union[str, bytes],
        iv: Union[str, bytes]
    ) -> str:
        """
        AES-CBC解密（小狐狸平台使用）
        
        Args:
            encrypted_data: 加密的数据（Base64编码）
            key: 密钥
            iv: 初始化向量
            
        Returns:
            解密后的文本
        """
        try:
            # 确保参数是bytes类型
            if isinstance(key, str):
                key = key.encode('utf-8')
            if isinstance(iv, str):
                iv = iv.encode('utf-8')
            
            # Base64解码
            if isinstance(encrypted_data, str):
                encrypted_data = base64.b64decode(encrypted_data)
            
            # 创建解密器
            cipher = AES.new(key, AES.MODE_CBC, iv)
            
            # 解密
            decrypted = cipher.decrypt(encrypted_data)
            
            # 去除填充
            unpadded = unpad(decrypted, AES.block_size)
            
            # 转为字符串
            result = unpadded.decode('utf-8')
            
            logger.debug(f"AES-CBC解密完成", encrypted_len=len(encrypted_data), decrypted_len=len(result))
            return result
            
        except Exception as e:
            logger.error(f"AES-CBC解密失败", error=str(e))
            raise DecryptError(f"AES-CBC解密失败: {str(e)}")
    
    @staticmethod
    def md5_hash(text: str) -> str:
        """
        MD5哈希
        
        Args:
            text: 要哈希的文本
            
        Returns:
            MD5哈希值（小写十六进制）
        """
        hash_value = hashlib.md5(text.encode('utf-8')).hexdigest()
        logger.debug(f"MD5哈希完成", text_len=len(text), hash=hash_value)
        return hash_value
    
    @staticmethod
    def sha256_hash(text: str) -> str:
        """
        SHA256哈希
        
        Args:
            text: 要哈希的文本
            
        Returns:
            SHA256哈希值（小写十六进制）
        """
        hash_value = hashlib.sha256(text.encode('utf-8')).hexdigest()
        logger.debug(f"SHA256哈希完成", text_len=len(text), hash=hash_value)
        return hash_value
    
    @staticmethod
    def base64_encode(data: Union[str, bytes]) -> str:
        """
        Base64编码
        
        Args:
            data: 要编码的数据
            
        Returns:
            Base64编码后的字符串
        """
        if isinstance(data, str):
            data = data.encode('utf-8')
        
        encoded = base64.b64encode(data).decode('utf-8')
        logger.debug(f"Base64编码完成", data_len=len(data), encoded_len=len(encoded))
        return encoded
    
    @staticmethod
    def base64_decode(encoded_data: str) -> bytes:
        """
        Base64解码
        
        Args:
            encoded_data: Base64编码的字符串
            
        Returns:
            解码后的字节数据
        """
        decoded = base64.b64decode(encoded_data)
        logger.debug(f"Base64解码完成", encoded_len=len(encoded_data), decoded_len=len(decoded))
        return decoded
    
    @staticmethod
    def hex_encode(data: Union[str, bytes]) -> str:
        """
        十六进制编码
        
        Args:
            data: 要编码的数据
            
        Returns:
            十六进制编码后的字符串
        """
        if isinstance(data, str):
            data = data.encode('utf-8')
        
        encoded = data.hex()
        logger.debug(f"十六进制编码完成", data_len=len(data), encoded_len=len(encoded))
        return encoded
    
    @staticmethod
    def hex_decode(encoded_data: str) -> bytes:
        """
        十六进制解码
        
        Args:
            encoded_data: 十六进制编码的字符串
            
        Returns:
            解码后的字节数据
        """
        decoded = bytes.fromhex(encoded_data)
        logger.debug(f"十六进制解码完成", encoded_len=len(encoded_data), decoded_len=len(decoded))
        return decoded 