# 直播聚合核心库开发规范

## 1. 项目概述

本项目是一个多平台直播聚合核心库，旨在提供统一的接口来访问多个直播平台的数据。

### 1.1 支持平台
- 番茄直播 (fanqie) - 复杂加密
- 绿茶直播 (lvcha) - AES加密
- 熊猫直播 (panda) - 需要代理
- Stripchat - 开放API
- Swag - 简单API
- 小狐狸 (xiaohuli) - JWT认证

## 2. 技术栈要求

### 2.1 核心库
- **httpx[http2]**: 现代化HTTP客户端，支持HTTP/2和异步
- **loguru**: 简洁强大的日志库
- **pydantic**: 数据验证和模型定义
- **orjson**: 高性能JSON序列化
- **cryptography/pycryptodome**: 加密支持

### 2.2 开发工具
- **pytest-asyncio**: 异步测试
- **black**: 代码格式化
- **ruff**: 代码检查（替代flake8）
- **mypy**: 类型检查
- **pre-commit**: Git钩子

## 3. 编码规范

### 3.1 Python编码规范
- 使用 Python 3.8+ 特性
- 遵循 PEP 8 规范
- 所有函数必须有类型注解
- 使用 asyncio 进行异步编程
- 使用 Pydantic 定义数据模型

### 3.2 命名规范
```python
# 类名：大驼峰
class PlatformAdapter:
    pass

# 函数/方法：小写下划线
async def get_live_list():
    pass

# 常量：大写下划线
DEFAULT_TIMEOUT = 30

# 私有方法：单下划线前缀
def _internal_method():
    pass
```

### 3.3 现代化代码风格
```python
# 使用 Pydantic 模型
from pydantic import BaseModel, Field, validator
from typing import Optional, List
from enum import Enum

class Platform(str, Enum):
    """平台枚举"""
    FANQIE = "fanqie"
    LVCHA = "lvcha"
    PANDA = "panda"

class UnifiedStreamer(BaseModel):
    """统一的主播数据模型"""
    id: str = Field(..., description="platform_userid 格式")
    platform: Platform
    username: str
    viewer_count: int = Field(default=0, ge=0)
    
    class Config:
        # 使用 orjson 提升性能
        json_loads = orjson.loads
        json_dumps = lambda v, *, default: orjson.dumps(v, default=default).decode()

# 使用 httpx 进行异步请求
async def fetch_data(self, url: str) -> dict:
    async with httpx.AsyncClient(http2=True) as client:
        response = await client.get(url)
        response.raise_for_status()
        return response.json()

# 使用 loguru 记录日志
from loguru import logger

logger.info("获取直播列表", platform=self.platform.value, page=page)
logger.error("请求失败", error=str(e), extra={"url": url})
```

### 3.4 文件组织
```
live_streams_core/
├── src/
│   ├── core/           # 核心模块
│   ├── platforms/      # 平台适配器
│   ├── utils/          # 工具模块
│   └── __init__.py
├── tests/              # 测试代码
├── docs/               # 文档
└── examples/           # 示例代码
```

## 4. 设计原则

### 4.1 SOLID原则
- **单一职责**：每个类只负责一项功能
- **开闭原则**：对扩展开放，对修改封闭
- **里氏替换**：子类可以替换父类
- **接口隔离**：接口精简且专注
- **依赖倒置**：依赖抽象而非具体实现

### 4.2 架构原则
- **统一接口**：所有平台使用相同的数据模型
- **异步优先**：使用异步IO提高并发性能
- **错误处理**：完善的异常处理和降级策略
- **可扩展性**：易于添加新平台
- **最小依赖**：减少外部依赖
- **现代化**：使用最新的Python特性和库

### 4.3 数据流原则
```
原始API → 平台适配器 → 数据转换 → 统一模型 → 客户端
```

## 5. 核心组件规范

### 5.1 数据模型
- 使用 `Pydantic` 定义数据结构
- 所有字段都要有类型注解
- 可选字段使用 `Optional[T]`
- 提供自动验证
- 使用 `Field` 添加描述和约束

### 5.2 平台适配器
- 继承 `PlatformAdapter` 基类
- 实现所有抽象方法
- 处理平台特定的加密/解密
- 统一错误处理
- 使用 httpx 进行网络请求

### 5.3 HTTP客户端
- 使用 httpx 的异步客户端
- 启用 HTTP/2 支持
- 连接池管理
- 自动重试机制
- 超时控制

## 6. 开发流程

### 6.1 开发步骤
1. **需求分析**：明确功能需求
2. **接口设计**：设计统一接口
3. **实现适配器**：实现平台特定逻辑
4. **编写测试**：单元测试和集成测试
5. **文档编写**：API文档和使用示例
6. **代码审查**：确保代码质量

### 6.2 新增平台流程
1. 分析平台API文档
2. 创建平台适配器类
3. 实现数据转换逻辑
4. 处理认证和加密
5. 编写测试用例
6. 更新文档

### 6.3 版本管理
- 使用语义化版本 (Semantic Versioning)
- 主版本号：不兼容的API修改
- 次版本号：向下兼容的功能性新增
- 修订号：向下兼容的问题修正

## 7. 测试规范

### 7.1 测试覆盖率
- 单元测试覆盖率 > 80%
- 所有公共方法都要有测试
- 异常情况必须测试

### 7.2 测试结构
```python
# tests/test_platform_name.py
import pytest
import httpx
from unittest.mock import AsyncMock

@pytest.mark.asyncio
class TestPlatformAdapter:
    async def test_get_live_list(self):
        """测试获取直播列表"""
        # 使用 httpx.AsyncClient 的 mock
        pass
    
    async def test_parse_streamer(self):
        """测试数据解析"""
        pass
```

## 8. 性能要求

### 8.1 响应时间
- 直播列表获取 < 3秒
- 流地址获取 < 2秒
- 并发请求响应 < 5秒

### 8.2 并发性能
- 支持同时请求多个平台
- 使用 httpx 的连接池
- 合理的超时和重试策略
- 使用 asyncio.gather 并发执行

## 9. 安全规范

### 9.1 敏感信息
- API密钥、Token等敏感信息使用环境变量
- 不在代码中硬编码密码
- 日志中不记录敏感信息

### 9.2 加密处理
- 使用标准加密库
- 密钥安全存储
- 定期更新密钥

## 10. 日志规范

### 10.1 使用 loguru
```python
from loguru import logger

# 配置日志
logger.add("logs/app_{time}.log", rotation="100 MB", retention="7 days")

# 记录日志
logger.info("开始获取直播列表", platform=platform, page=page)
logger.debug("API响应", response=response_data)
logger.error("请求失败", error=str(e), traceback=True)
logger.warning("重试请求", attempt=attempt, url=url)
```

### 10.2 日志级别
- DEBUG：详细调试信息
- INFO：一般信息
- WARNING：警告信息
- ERROR：错误信息（包含堆栈）
- CRITICAL：严重错误

## 11. 文档规范

### 11.1 代码注释
- 所有公共方法都要有docstring
- 复杂逻辑要有行内注释
- 使用类型注解替代类型说明

### 11.2 Docstring格式
```python
async def get_live_list(
    self, 
    category: str = "hot",
    page: int = 1,
    size: int = 20
) -> List[UnifiedStreamer]:
    """获取直播列表
    
    Args:
        category: 分类（hot/new/vip）
        page: 页码，从1开始
        size: 每页数量，默认20
        
    Returns:
        统一格式的主播列表
        
    Raises:
        PlatformException: 平台API错误
        NetworkException: 网络请求错误
    """
    pass
```

## 12. 依赖管理

### 12.1 使用 Poetry
```toml
[tool.poetry]
name = "live-streams-core"
version = "0.1.0"
python = "^3.8"

[tool.poetry.dependencies]
httpx = {extras = ["http2"], version = "^0.24.0"}
loguru = "^0.7.0"
pydantic = "^2.0.0"
orjson = "^3.9.0"
cryptography = "^41.0.0"

[tool.poetry.group.dev.dependencies]
pytest-asyncio = "^0.21.0"
black = "^23.0.0"
ruff = "^0.0.280"
mypy = "^1.4.0"
```

### 12.2 工具配置
```toml
[tool.ruff]
line-length = 100
select = ["E", "F", "I", "N", "W", "B", "C90", "UP"]

[tool.black]
line-length = 100

[tool.mypy]
python_version = "3.8"
warn_return_any = true
``` 