# Live Streams Core - 多平台直播聚合核心库

一个强大的Python库，用于聚合多个直播平台的数据，提供统一的接口访问各平台直播信息。

## 特性

- 🎯 **统一接口**：所有平台使用相同的数据模型和API
- 🚀 **异步支持**：基于asyncio的高性能异步IO
- 🔐 **多种加密**：支持XOR、AES等多种加密算法
- 💾 **智能缓存**：多层缓存机制，提升访问速度
- 🌍 **多平台支持**：
  - 番茄直播 (复杂加密)
  - 绿茶直播 (AES加密)
  - 熊猫直播 (需要代理)
  - Stripchat (国际平台)
  - Swag (简单API)
  - 小狐狸 (JWT认证)
- 🛡️ **错误处理**：完善的异常处理和降级策略
- 📊 **并发控制**：智能的并发请求管理
- 🔧 **易于扩展**：简单添加新平台支持

## 快速开始

### 安装

```bash
pip install live-streams-core
```

### 基础使用

```python
import asyncio
from live_streams_core import PlatformManager, Platform

async def main():
    # 创建平台管理器
    manager = PlatformManager()
    
    # 获取单个平台的直播列表
    adapter = manager.get_adapter(Platform.SWAG)
    streamers = await adapter.get_live_list(page=1, size=20)
    
    for streamer in streamers:
        print(f"{streamer.nickname} - {streamer.title} ({streamer.viewer_count}人观看)")
        print(f"直播流: {streamer.get_best_stream_url()}")
    
    # 获取所有平台的直播列表
    all_streamers = await manager.get_all_live_list()
    print(f"所有平台共 {len(all_streamers)} 个直播间")

asyncio.run(main())
```

### 高级配置

```python
# 自定义配置
config = {
    "fanqie": {
        "auth_token": "your_token_here",
        "timeout": 20
    },
    "panda": {
        "cookie": "your_cookie_here",
        "use_proxy": True
    },
    "cache": {
        "redis_url": "redis://localhost:6379",
        "enable_local_cache": True
    }
}

manager = PlatformManager(config)

# 获取直播流地址
stream_qualities = await adapter.get_stream_url("room_id")
for quality in stream_qualities:
    print(f"{quality.quality}: {quality.url}")
```

## 数据模型

### UnifiedStreamer - 统一的主播模型

```python
@dataclass
class UnifiedStreamer:
    # 标识信息
    id: str                    # 统一ID格式: platform_userid
    platform: Platform         # 平台枚举
    platform_user_id: str      # 平台用户ID
    platform_room_id: str      # 平台房间ID
    
    # 基本信息
    username: str              # 用户名
    nickname: str              # 昵称
    avatar_url: str            # 头像URL
    cover_url: str             # 封面URL
    
    # 直播状态
    is_live: bool              # 是否在直播
    title: str                 # 直播标题
    viewer_count: int          # 观看人数
    start_time: datetime       # 开始时间
    
    # 流信息
    stream_qualities: List[StreamQuality]  # 可用画质
    stream_type: StreamType    # 流类型(FLV/HLS/RTMP)
    
    # 访问限制
    is_vip: bool               # 是否收费
    need_password: bool        # 是否需要密码
    need_login: bool           # 是否需要登录
```

## 支持的平台

| 平台 | 特点 | 认证要求 | 特殊说明 |
|------|------|----------|----------|
| 番茄直播 | 三层加密+MD5签名 | Token | 最复杂的加密机制 |
| 绿茶直播 | AES-ECB加密 | Token | 流地址需要解密 |
| 熊猫直播 | 韩国平台 | Cookie | 需要代理访问 |
| Stripchat | 国际平台 | 无 | 开放API，HLS流 |
| Swag | 简单API | 无 | 直接FLV地址 |
| 小狐狸 | 双重加密 | JWT | 动态密钥解密 |

## 项目结构

```
live_streams_core/
├── src/
│   ├── core/           # 核心模块
│   ├── platforms/      # 平台适配器
│   └── utils/          # 工具模块
├── tests/              # 测试代码
├── docs/               # 文档
└── examples/           # 示例代码
```

## 开发指南

查看 [DEVELOPMENT_FLOW.md](DEVELOPMENT_FLOW.md) 了解开发流程。

查看 [RULES.md](RULES.md) 了解编码规范。

查看 [DESIGN.md](DESIGN.md) 了解架构设计。

## 贡献

欢迎提交Issue和Pull Request！

## 许可证

MIT License

## 更新日志

查看 [CHANGELOG.md](CHANGELOG.md) 了解版本更新信息。 