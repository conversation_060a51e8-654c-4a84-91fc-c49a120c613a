"""
番茄平台适配器

番茄平台使用复杂的多层加密机制：
- API路径双层编码（Base64 + 十六进制）
- 请求体和响应体XOR异或加密
- MD5签名验证
"""

import base64
import binascii
import hashlib
import json
import random
import time
from datetime import datetime
from typing import Any, Dict, List, Optional

from loguru import logger

from ..core.base import PlatformAdapter
from ..core.exceptions import AuthenticationError, PlatformException
from ..core.models import Platform, StreamQuality, StreamType, UnifiedStreamer
from ..utils.crypto import CryptoUtils


class FanqieAdapter(PlatformAdapter):
    """番茄平台适配器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化适配器
        
        Args:
            config: 配置信息，包含以下参数:
                认证参数（必需）:
                - authorization: 认证token（必需）
                - device_id: 设备ID（可选，生成默认值）
                
                网络参数（可选）:
                - base_url: 自定义API域名（可选，默认: https://api.dandongrf.com）
        """
        super().__init__(config)
        
        # 从配置中获取认证信息
        self.auth_token = self.config.get("authorization", "")
        self.device_id = self.config.get("device_id", f"fanqie_device_{int(time.time())}")
        
        # 检查是否已认证
        self.is_authenticated = bool(self.auth_token)
        
        # XOR加密参数
        self.XOR_BASE = 202030
        self.XOR_OFFSET = 196608
        self.XOR_KEY = self.XOR_BASE - self.XOR_OFFSET  # 5422
    
    def _get_platform(self) -> Platform:
        """获取平台标识"""
        return Platform.FANQIE
    
    def _get_base_url(self) -> str:
        """获取基础URL"""
        # 支持自定义域名，如果配置中有base_url则使用，否则使用默认值
        return self.config.get("base_url", "https://api.dandongrf.com")
    
    def _build_headers(self) -> Dict[str, str]:
        """构建请求头"""
        timestamp = str(int(time.time() * 1000))
        
        headers = {
            "Content-Type": "application/json",
            "Accept": "*/*",
            "authorization": self.auth_token,
            "x-auth-timestamp": timestamp,
            "version": "302",
            "terminal": "3",
            "ak": "jyzb",
            "channel": "1"
        }
        
        return headers
    
    def _check_authentication(self):
        """检查是否已认证"""
        if not self.is_authenticated:
            raise AuthenticationError(
                "未提供认证信息。请在创建适配器时传入authorization参数"
            )
    
    # ========== 加密解密相关方法 ==========
    
    def _xor_encrypt(self, text: str) -> str:
        """XOR异或加密"""
        result = ''
        for char in text:
            try:
                result += chr(ord(char) ^ self.XOR_KEY)
            except Exception:
                result += '\uFFFD'  # 错误字符替换
        return result
    
    def _xor_decrypt(self, encrypted_text: str) -> str:
        """XOR异或解密"""
        result = ''
        for char in encrypted_text:
            try:
                char_code = ord(char) ^ self.XOR_KEY
                if 0 <= char_code <= 0x10FFFF and not (0xD800 <= char_code <= 0xDFFF):
                    result += chr(char_code)
                else:
                    result += '\uFFFD'  # 无效字符替换
            except Exception:
                result += '\uFFFD'
        return result
    
    def _encode_api_path(self, api_endpoint: str) -> str:
        """编码API路径（Base64 + 十六进制 + 随机前缀）"""
        # 第一层：Base64编码
        base64_bytes = base64.b64encode(api_endpoint.encode('utf-8'))
        base64_str = base64_bytes.decode('ascii')
        
        # 第二层：十六进制编码
        hex_values = []
        for char in base64_str:
            char_code = ord(char)
            hex_value = format(char_code, "02x")
            hex_values.append(hex_value)
        hex_str = "".join(hex_values)
        
        # 添加随机前缀（3位数字）
        random_nums = random.sample(range(1, 10), 3)
        prefix = ''.join(map(str, random_nums))
        
        return f"/{prefix}{hex_str}"
    
    def _generate_signature(self, api_endpoint: str, params: Dict[str, Any]) -> str:
        """生成MD5签名"""
        timestamp = str(int(time.time() * 1000))
        
        # 构建基础参数
        base_params = {
            "ak": "jyzb",
            "authorization": self.auth_token,
            "channel": "1",
            "terminal": "3",
            "version": "302",
            "x-auth-timestamp": timestamp
        }
        
        # 合并所有参数
        data = {**base_params, **params}
        
        # 按键名排序并构建查询字符串
        query_string = "&".join([f"{k}={v}" for k, v in sorted(data.items())])
        
        # 构建签名字符串
        sign_string = f"{api_endpoint}?{query_string}oknzoiwe@23"
        
        # 计算MD5哈希
        return hashlib.md5(sign_string.encode('utf-8')).hexdigest()
    
    def _encrypt_request_body(self, data: Dict[str, Any]) -> str:
        """加密请求体"""
        json_str = json.dumps(data, separators=(',', ':'))
        xor_result = self._xor_encrypt(json_str)
        base64_result = base64.b64encode(xor_result.encode()).decode()
        return binascii.hexlify(base64_result.encode()).decode()
    
    def _decrypt_response_body(self, encrypted_data: str) -> Dict[str, Any]:
        """解密响应体"""
        try:
            # 十六进制解码
            hex_decoded = binascii.unhexlify(encrypted_data)
            # Base64解码
            base64_decoded = base64.b64decode(hex_decoded).decode()
            # XOR解密
            decrypted_text = self._xor_decrypt(base64_decoded)
            # JSON解析
            return json.loads(decrypted_text)
        except Exception as e:
            raise PlatformException(f"解密响应失败: {str(e)}")
    
    async def _fanqie_request(self, api_endpoint: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """番茄平台专用请求方法"""
        self._check_authentication()
        
        # 生成签名
        signature = self._generate_signature(api_endpoint, params)
        
        # 编码API路径
        encoded_path = self._encode_api_path(api_endpoint)
        
        # 加密请求体
        encrypted_body = self._encrypt_request_body(params)
        
        # 构建请求头
        timestamp = str(int(time.time() * 1000))
        headers = {
            "Content-Type": "application/json",
            "Accept": "*/*",
            "authorization": self.auth_token,
            "x-auth-sign": signature,
            "x-auth-timestamp": timestamp,
            "version": "302",
            "terminal": "3",
            "ak": "jyzb",
            "channel": "1"
        }
        
        # 发送请求
        response = await self.http_client.post(
            encoded_path,
            data=encrypted_body,
            headers=headers
        )
        
        # 解密响应
        if response.text:
            return self._decrypt_response_body(response.text)
        else:
            raise PlatformException("响应体为空")
    
    # ========== 统一接口实现 ==========
    
    async def get_live_list(
        self,
        category: str = "hot",
        page: int = 1,
        size: int = 20,
        **kwargs
    ) -> List[UnifiedStreamer]:
        """获取直播列表 - 只返回统一格式"""
        response = await self.get_live_list_with_raw(
            category=category,
            page=page,
            size=size,
            **kwargs
        )
        return response.items
    
    async def _fetch_live_list_raw(
        self,
        category: str,
        page: int,
        size: int,
        **kwargs
    ) -> Dict[str, Any]:
        """获取直播列表的原始响应"""
        # 番茄的分类映射
        category_map = {
            "hot": 2,          # 热门列表
            "new": 4,          # 新人列表
            "vip": 5,          # 收费列表
            "private": 10,     # 1v1表演
            "follow": "follow", # 关注列表（特殊处理）
            "beauty": "beauty"  # 颜值列表（特殊处理）
        }
        
        fanqie_category = category_map.get(category, 2)
        
        if category == "follow":
            # 关注列表使用不同的API
            return await self._fanqie_request("/home/<USER>/attention/list", {
                "type": 2,
                "pageNo": page,
                "pageSize": size
            })
        elif category == "beauty":
            # 颜值列表使用不同的API
            return await self._fanqie_request("/home/<USER>/queryById", {
                "id": 3,
                "pageNo": page,
                "pageSize": size
            })
        else:
            # 常规分类列表
            return await self._fanqie_request("/home/<USER>/cls/query", {
                "type": fanqie_category,
                "pageNo": page,
                "pageSize": size
            })
    
    def _extract_items_from_response(self, response: Dict[str, Any]) -> List[Dict[str, Any]]:
        """从响应中提取数据项列表"""
        if response.get("success") and "data" in response:
            return response["data"]
        
        self.logger.warning("无法从响应中提取数据列表", response_keys=list(response.keys()))
        return []
    
    def _extract_total_from_response(self, response: Dict[str, Any]) -> Optional[int]:
        """从响应中提取总数"""
        return response.get("count")
    
    def parse_streamer(self, raw_data: Dict[str, Any]) -> UnifiedStreamer:
        """解析原始数据为统一格式"""
        # 提取关键字段
        user_id = str(raw_data.get("userId", ""))
        room_id = str(raw_data.get("roomId", ""))
        
        # 解析直播状态
        is_live = raw_data.get("state") == 1
        
        # 解析观看人数
        viewer_count = int(raw_data.get("currentHot", "0"))
        
        # 解析付费信息
        pay_type = raw_data.get("payType", 0)
        is_vip = pay_type > 0
        money_fen = raw_data.get("moneyFen", 0)
        
        # 构建头像和封面URL
        icon = raw_data.get("icon", "")
        room_cover = raw_data.get("roomCover", "")
        
        # 补全URL（如果是相对路径）
        if icon and not icon.startswith("http"):
            icon = f"https://static.dandongrf.com{icon}"
        if room_cover and not room_cover.startswith("http"):
            room_cover = f"https://static.dandongrf.com{room_cover}"
        
        # 解析标签
        tags = []
        if raw_data.get("newFlag") == 1:
            tags.append("新人")
        if raw_data.get("vibratingEgg") == 1:
            tags.append("震动蛋")
        if raw_data.get("weekStar"):
            tags.append("周星")
        
        # 构建统一数据模型
        streamer = UnifiedStreamer(
            id=self._build_unified_id(user_id),
            platform=self.platform,
            platform_user_id=user_id,
            platform_room_id=room_id,
            username=user_id,
            nickname=raw_data.get("userName", ""),
            avatar_url=icon,
            cover_url=room_cover,
            is_live=is_live,
            title=raw_data.get("roomName", ""),
            viewer_count=viewer_count,
            start_time=None,  # 番茄API不提供开播时间
            category="",
            tags=tags,
            stream_qualities=[],  # 流地址需要单独获取
            is_vip=is_vip,
            need_password=False,
            need_login=True,  # 番茄平台需要认证
            raw_data=raw_data
        )
        
        # 添加平台特定字段
        streamer.extra_fields.update({
            "invite_code": raw_data.get("inviteCode", ""),
            "live_type": raw_data.get("liveType", 0),
            "pay_desc": raw_data.get("payDesc", ""),
            "money_fen": money_fen,
            "fans_count": raw_data.get("fansCnt", "0"),
            "love_count": raw_data.get("loveCnt", "0"),
            "attention_count": raw_data.get("attenCnt", "0"),
            "one_to_one_state": raw_data.get("oneToOneState", 0),
            "vibrating_egg": raw_data.get("vibratingEgg", 0),
            "game_name": raw_data.get("gameName", ""),
            "anchor_from": raw_data.get("anchorFrom", 0)
        })
        
        return streamer
    
    async def get_stream_url(self, room_id: str) -> List[StreamQuality]:
        """获取直播流地址"""
        self._check_authentication()
        
        try:
            response = await self._fanqie_request("/app/live/joinRoom", {
                "liveId": room_id
            })
            
            stream_qualities = []
            
            # 检查响应是否成功
            if response.get("success") and "data" in response:
                data = response["data"]
                
                # 提取直播流地址
                if "pullStreamAddr" in data and data["pullStreamAddr"]:
                    stream_qualities.append(
                        StreamQuality(
                            quality="origin",
                            url=data["pullStreamAddr"],
                            format=StreamType.FLV
                        )
                    )
            
            return stream_qualities
            
        except Exception as e:
            self.logger.error("获取流地址失败", room_id=room_id, error=str(e))
            return []
    
    # ========== 番茄平台特定方法 ==========
    
    async def get_fans_group(self, room_id: str) -> Dict[str, Any]:
        """
        获取粉丝团信息 - 番茄平台特定功能
        
        Args:
            room_id: 房间ID
            
        Returns:
            粉丝团信息
        """
        self._check_authentication()
        
        response = await self._fanqie_request("/home/<USER>/fanGroup/info", {
            "roomId": room_id
        })
        
        if response.get("success"):
            return response.get("data", {})
        else:
            raise PlatformException(f"获取粉丝团信息失败: {response.get('msg')}")
    
    async def get_room_detail(self, room_id: str) -> Dict[str, Any]:
        """
        获取房间详细信息 - 番茄平台特定功能
        
        Args:
            room_id: 房间ID
            
        Returns:
            房间详细信息
        """
        self._check_authentication()
        
        response = await self._fanqie_request("/home/<USER>/roomDetail", {
            "roomId": room_id
        })
        
        if response.get("success"):
            return response.get("data", {})
        else:
            raise PlatformException(f"获取房间详情失败: {response.get('msg')}")
    
    def update_auth(self, authorization: str, device_id: Optional[str] = None):
        """
        更新认证信息
        
        Args:
            authorization: 新的认证token
            device_id: 新的设备ID（可选）
        """
        self.auth_token = authorization
        if device_id is not None:
            self.device_id = device_id
        
        self.is_authenticated = bool(self.auth_token)
        self.logger.info("认证信息已更新") 