"""
熊猫平台适配器

熊猫平台特点：
- 韩国平台，需要代理访问
- Cookie认证机制
- HLS流媒体格式
- 特殊的X-Device-Info请求头
"""

import json
from datetime import datetime
from typing import Any, Dict, List, Optional

from loguru import logger

from ..core.base import PlatformAdapter
from ..core.exceptions import AuthenticationError, PlatformException
from ..core.models import Platform, StreamQuality, StreamType, UnifiedStreamer


class PandaAdapter(PlatformAdapter):
    """熊猫平台适配器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化适配器
        
        Args:
            config: 配置信息，包含以下参数:
                认证参数（必需）:
                - cookies: Cookie字符串（必需）
                - user_login_idx: 用户登录ID（必需）
                - proxy: 代理配置（可选）
                
                网络参数（可选）:
                - base_url: 自定义API域名（可选，默认: https://api.pandalive.co.kr）
        """
        super().__init__(config)
        
        # 从配置中获取认证信息
        self.cookies = self.config.get("cookies", "")
        self.user_login_idx = self.config.get("user_login_idx", "")
        
        # 检查是否已认证
        self.is_authenticated = bool(self.cookies and self.user_login_idx)
        
        # 代理配置（韩国平台需要代理）
        self.proxy_config = self.config.get("proxy", {
            "http": "************************************************",
            "https": "************************************************"
        })
    
    def _get_platform(self) -> Platform:
        """获取平台标识"""
        return Platform.PANDA
    
    def _get_base_url(self) -> str:
        """获取基础URL"""
        # 支持自定义域名，如果配置中有base_url则使用，否则使用默认值
        return self.config.get("base_url", "https://api.pandalive.co.kr")
    
    def _build_headers(self) -> Dict[str, str]:
        """构建请求头"""
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/127.0.0.0 Safari/537.36",
            "Content-Type": "application/x-www-form-urlencoded"
        }
        
        # 添加Cookie认证
        if self.cookies:
            headers["Cookie"] = self.cookies
        
        # 添加设备信息
        if self.user_login_idx:
            x_device_info = {
                "t": "webPc",
                "v": "1.0",
                "ui": int(self.user_login_idx)
            }
            headers["X-Device-Info"] = json.dumps(x_device_info, separators=(',', ':'))
        
        return headers
    
    def _check_authentication(self):
        """检查是否已认证"""
        if not self.is_authenticated:
            raise AuthenticationError(
                "未提供认证信息。请在创建适配器时传入cookies和user_login_idx参数"
            )
    
    async def _panda_request(self, method: str, url: str, **kwargs) -> Dict[str, Any]:
        """熊猫平台专用请求方法（带代理）"""
        self._check_authentication()
        
        # 添加代理配置
        if self.proxy_config:
            kwargs["proxies"] = self.proxy_config
        
        return await self.raw_request(method, url, **kwargs)
    
    # ========== 统一接口实现 ==========
    
    async def get_live_list(
        self,
        category: str = "hot",
        page: int = 1,
        size: int = 20,
        **kwargs
    ) -> List[UnifiedStreamer]:
        """获取直播列表 - 只返回统一格式"""
        response = await self.get_live_list_with_raw(
            category=category,
            page=page,
            size=size,
            **kwargs
        )
        return response.items
    
    async def _fetch_live_list_raw(
        self,
        category: str,
        page: int,
        size: int,
        **kwargs
    ) -> Dict[str, Any]:
        """获取直播列表的原始响应"""
        # 熊猫的排序映射
        order_map = {
            "hot": "hot",        # 热门
            "new": "new",        # 最新
            "user": "user",      # 观看人数
            "viewers": "user"    # 观看人数（别名）
        }
        
        order_by = order_map.get(category, "hot")
        only_new_bj = "Y" if category == "new" else "N"
        
        # 计算偏移量
        offset = (page - 1) * size
        
        # 构建请求参数
        params = {
            "offset": offset,
            "limit": min(size, 100),  # 最大100
            "orderBy": order_by,
            "onlyNewBj": only_new_bj
        }
        
        # 发送请求
        response = await self._panda_request(
            "POST",
            "/v1/live",
            data=params
        )
        
        if not response.get("result"):
            raise PlatformException(f"获取直播列表失败: {response.get('message')}")
        
        return response
    
    def _extract_items_from_response(self, response: Dict[str, Any]) -> List[Dict[str, Any]]:
        """从响应中提取数据项列表"""
        return response.get("list", [])
    
    def _extract_total_from_response(self, response: Dict[str, Any]) -> Optional[int]:
        """从响应中提取总数"""
        page_info = response.get("page", {})
        return page_info.get("total")
    
    def parse_streamer(self, raw_data: Dict[str, Any]) -> UnifiedStreamer:
        """解析原始数据为统一格式"""
        # 提取关键字段
        user_id = str(raw_data.get("userId", ""))
        user_idx = str(raw_data.get("userIdx", ""))
        code = raw_data.get("code", "")
        
        # 解析直播状态
        is_live = raw_data.get("isLive", False)
        
        # 解析观看人数
        viewer_count = raw_data.get("user", 0)
        
        # 解析付费信息
        room_type = raw_data.get("type", "free")
        is_vip = room_type != "free"
        is_adult = raw_data.get("isAdult", False)
        need_password = raw_data.get("isPw", False)
        
        # 解析开播时间
        start_time = None
        if raw_data.get("startTime") and raw_data["startTime"] != "0000-00-00 00:00:00":
            try:
                start_time = datetime.strptime(raw_data["startTime"], "%Y-%m-%d %H:%M:%S")
            except ValueError:
                pass
        
        # 构建头像和封面URL
        thumb_url = raw_data.get("thumbUrl", "")
        user_img = raw_data.get("userImg", "")
        
        # 解析标签
        tags = []
        if raw_data.get("newBjYN") == "Y":
            tags.append("新主播")
        if is_adult:
            tags.append("成人内容")
        if raw_data.get("isGuestLive"):
            tags.append("嘉宾直播")
        
        # 添加分类标签
        category_name = raw_data.get("category", "")
        if category_name:
            tags.append(f"分类:{category_name}")
        
        # 构建统一数据模型
        streamer = UnifiedStreamer(
            id=self._build_unified_id(user_id),
            platform=self.platform,
            platform_user_id=user_id,
            platform_room_id=code,
            username=user_id,
            nickname=raw_data.get("userNick", ""),
            avatar_url=user_img,
            cover_url=thumb_url,
            is_live=is_live,
            title=raw_data.get("title", ""),
            viewer_count=viewer_count,
            start_time=start_time,
            category=category_name,
            tags=tags,
            stream_qualities=[],  # 流地址需要单独获取
            is_vip=is_vip,
            need_password=need_password,
            need_login=True,  # 熊猫平台需要认证
            raw_data=raw_data
        )
        
        # 添加平台特定字段
        streamer.extra_fields.update({
            "user_idx": user_idx,
            "room_code": code,
            "user_limit": raw_data.get("userLimit", 0),
            "size_width": raw_data.get("sizeWidth", 0),
            "size_height": raw_data.get("sizeHeight", 0),
            "browser": raw_data.get("browser", ""),
            "heart": raw_data.get("heart", 0),
            "play_count": raw_data.get("playCnt", 0),
            "like_count": raw_data.get("likeCnt", 0),
            "bookmark_count": raw_data.get("bookmarkCnt", 0),
            "fan_count": raw_data.get("fanCnt", 0),
            "total_score_count": raw_data.get("totalScoreCnt", 0),
            "storage": raw_data.get("storage", ""),
            "on_air_type": raw_data.get("onAirType", ""),
            "live_type": raw_data.get("liveType", "")
        })
        
        return streamer
    
    async def get_stream_url(self, room_id: str) -> List[StreamQuality]:
        """获取直播流地址"""
        self._check_authentication()
        
        try:
            # 从room_id中提取userId（熊猫平台需要userId而不是code）
            # 如果room_id是code格式，需要先获取用户信息
            user_id = self._extract_user_id_from_room_id(room_id)
            
            # 获取播放信息
            response = await self._panda_request(
                "POST",
                "/v1/live/play",
                data={
                    "action": "watch",
                    "userId": user_id
                }
            )
            
            if not response.get("result"):
                self.logger.error("获取播放信息失败", room_id=room_id, msg=response.get("message"))
                return []
            
            media = response.get("media", {})
            stream_qualities = []
            
            # 提取HLS播放列表
            if "hlsPlaylist" in media and media["hlsPlaylist"]:
                stream_qualities.append(
                    StreamQuality(
                        quality="origin",
                        url=media["hlsPlaylist"],
                        format=StreamType.HLS
                    )
                )
            
            # 如果有多个预设质量
            presets = media.get("presets", [])
            for preset in presets:
                if preset != "origin":  # 避免重复
                    # 构建不同质量的HLS URL
                    hls_url = media.get("hlsPlaylist", "")
                    if hls_url:
                        quality_url = hls_url.replace("master/", f"{preset}/")
                        stream_qualities.append(
                            StreamQuality(
                                quality=preset,
                                url=quality_url,
                                format=StreamType.HLS
                            )
                        )
            
            return stream_qualities
            
        except Exception as e:
            self.logger.error("获取流地址失败", room_id=room_id, error=str(e))
            return []
    
    def _extract_user_id_from_room_id(self, room_id: str) -> str:
        """从room_id中提取userId"""
        # 如果room_id是code格式（包含下划线），提取userIdx部分
        if "_" in room_id:
            # code格式: "userIdx_timestamp"
            return room_id.split("_")[0]
        else:
            # 假设直接是userId
            return room_id
    
    # ========== 熊猫平台特定方法 ==========
    
    async def get_room_detail(self, user_id: str) -> Dict[str, Any]:
        """
        获取房间详细信息 - 熊猫平台特定功能
        
        Args:
            user_id: 主播用户ID
            
        Returns:
            房间详细信息，包括播放信息、粉丝列表等
        """
        self._check_authentication()
        
        response = await self._panda_request(
            "POST",
            "/v1/live/play",
            data={
                "action": "watch",
                "userId": user_id
            }
        )
        
        if not response.get("result"):
            raise PlatformException(f"获取房间详情失败: {response.get('message')}")
        
        return response
    
    async def get_fan_ranking(self, user_id: str) -> List[Dict[str, Any]]:
        """
        获取粉丝排行榜 - 熊猫平台特定功能
        
        Args:
            user_id: 主播用户ID
            
        Returns:
            粉丝排行榜列表
        """
        room_detail = await self.get_room_detail(user_id)
        return room_detail.get("fanRankingList", [])
    
    async def search_streamers(self, keyword: str, page: int = 1, size: int = 20) -> List[Dict[str, Any]]:
        """
        搜索主播 - 熊猫平台特定功能
        
        Args:
            keyword: 搜索关键词
            page: 页码
            size: 每页数量
            
        Returns:
            搜索结果列表
        """
        self._check_authentication()
        
        offset = (page - 1) * size
        
        response = await self._panda_request(
            "POST",
            "/v1/search/live",
            data={
                "keyword": keyword,
                "offset": offset,
                "limit": min(size, 100)
            }
        )
        
        if response.get("result"):
            return response.get("list", [])
        else:
            raise PlatformException(f"搜索失败: {response.get('message')}")
    
    def update_auth(self, cookies: str, user_login_idx: str):
        """
        更新认证信息
        
        Args:
            cookies: 新的Cookie字符串
            user_login_idx: 新的用户登录ID
        """
        self.cookies = cookies
        self.user_login_idx = user_login_idx
        self.is_authenticated = bool(self.cookies and self.user_login_idx)
        
        # 更新请求头
        if self.http_client and self.http_client.client:
            self.http_client.client.headers["Cookie"] = self.cookies
            x_device_info = {
                "t": "webPc",
                "v": "1.0",
                "ui": int(self.user_login_idx)
            }
            self.http_client.client.headers["X-Device-Info"] = json.dumps(x_device_info, separators=(',', ':'))
        
        self.logger.info("认证信息已更新") 