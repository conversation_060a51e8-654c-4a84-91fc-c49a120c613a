"""
Swag平台适配器

Swag是最简单的平台，直接返回FLV流地址，无需复杂解密
"""

from datetime import datetime
from typing import Any, Dict, List, Optional

from ..core.base import PlatformAdapter
from ..core.models import Platform, StreamQuality, StreamType, UnifiedStreamer


class SwagAdapter(PlatformAdapter):
    """Swag平台适配器
    
    配置参数:
        - base_url: 自定义API域名（可选，默认: https://capi.swag.live）
    """
    
    def _get_platform(self) -> Platform:
        """获取平台标识"""
        return Platform.SWAG
    
    def _get_base_url(self) -> str:
        """获取基础URL"""
        # 支持自定义域名，如果配置中有base_url则使用，否则使用默认值
        return self.config.get("base_url", "https://capi.swag.live")
    
    def _build_headers(self) -> Dict[str, str]:
        """构建请求头"""
        headers = super()._build_headers()
        headers.update({
            "Referer": "https://swag.live/",
            "Origin": "https://swag.live"
        })
        return headers
    
    async def get_live_list(
        self,
        category: str = "hot",
        page: int = 1,
        size: int = 20,
        **kwargs
    ) -> List[UnifiedStreamer]:
        """获取直播列表 - 只返回统一格式"""
        response = await self.get_live_list_with_raw(
            category=category,
            page=page,
            size=size,
            **kwargs
        )
        return response.items
    
    async def _fetch_live_list_raw(
        self,
        category: str,
        page: int,
        size: int,
        **kwargs
    ) -> Dict[str, Any]:
        """获取直播列表的原始响应"""
        # Swag的分类映射
        category_map = {
            "hot": "hot",
            "new": "new",
            "taiwan": "taiwan",
            "korea": "korea",
            "japan": "japan",
            "europe": "europe"
        }
        
        swag_category = category_map.get(category, "hot")
        
        # 构建请求参数
        params = {
            "sort": swag_category,
            "page": page,
            "limit": size
        }
        
        # 发送请求
        response = await self.raw_request(
            "GET",
            "/home/<USER>/list",
            params=params
        )
        
        return response
    
    def _extract_items_from_response(self, response: Dict[str, Any]) -> List[Dict[str, Any]]:
        """从响应中提取数据项列表"""
        # Swag的响应格式：{"code": 200, "data": {"list": [...], "total": 100}}
        if response.get("code") == 200 and "data" in response:
            data = response["data"]
            if isinstance(data, dict) and "list" in data:
                return data["list"]
        
        self.logger.warning("无法从Swag响应中提取数据", response_code=response.get("code"))
        return []
    
    def _extract_total_from_response(self, response: Dict[str, Any]) -> Optional[int]:
        """从响应中提取总数"""
        if response.get("code") == 200 and "data" in response:
            data = response["data"]
            if isinstance(data, dict) and "total" in data:
                return data["total"]
        return None
    
    def parse_streamer(self, raw_data: Dict[str, Any]) -> UnifiedStreamer:
        """解析原始数据为统一格式"""
        # 提取关键字段
        user_id = str(raw_data.get("userId", ""))
        room_id = str(raw_data.get("roomId", user_id))
        
        # 解析直播状态
        is_live = raw_data.get("status") == 1
        
        # 解析流地址
        stream_qualities = []
        if is_live and "streamUrl" in raw_data:
            # Swag直接提供FLV流地址
            stream_url = raw_data["streamUrl"]
            if stream_url:
                stream_qualities.append(
                    StreamQuality(
                        quality="origin",
                        url=stream_url,
                        format=StreamType.FLV
                    )
                )
        
        # 解析观看人数
        viewer_count = raw_data.get("viewerCount", 0)
        if isinstance(viewer_count, str):
            # 处理 "1.2k" 这样的格式
            viewer_count = self._parse_viewer_count(viewer_count)
        
        # 构建统一数据模型
        streamer = UnifiedStreamer(
            id=self._build_unified_id(user_id),
            platform=self.platform,
            platform_user_id=user_id,
            platform_room_id=room_id,
            username=raw_data.get("username", ""),
            nickname=raw_data.get("nickname", raw_data.get("username", "")),
            avatar_url=raw_data.get("avatar", ""),
            cover_url=raw_data.get("cover", ""),
            is_live=is_live,
            title=raw_data.get("title", ""),
            viewer_count=viewer_count,
            start_time=self._parse_timestamp(raw_data.get("startTime")),
            category=raw_data.get("category", ""),
            tags=raw_data.get("tags", []) if isinstance(raw_data.get("tags"), list) else [],
            stream_qualities=stream_qualities,
            is_vip=raw_data.get("isVip", False),
            need_password=raw_data.get("needPassword", False),
            need_login=False,  # Swag不需要登录即可观看
            raw_data=raw_data
        )
        
        # 添加平台特定字段到extra_fields
        streamer.extra_fields.update({
            "country": raw_data.get("country", ""),
            "city": raw_data.get("city", ""),
            "age": raw_data.get("age"),
            "followerCount": raw_data.get("followerCount", 0),
            "level": raw_data.get("level", 0)
        })
        
        return streamer
    
    async def get_stream_url(self, room_id: str) -> List[StreamQuality]:
        """获取直播流地址"""
        # 获取房间详情
        response = await self.raw_request(
            "GET",
            f"/room/info/{room_id}"
        )
        
        if response.get("code") != 200:
            self.logger.error(f"获取房间信息失败", room_id=room_id, code=response.get("code"))
            return []
        
        data = response.get("data", {})
        
        # 检查直播状态
        if data.get("status") != 1:
            self.logger.info(f"主播未在直播", room_id=room_id)
            return []
        
        # 提取流地址
        stream_qualities = []
        stream_url = data.get("streamUrl", "")
        
        if stream_url:
            stream_qualities.append(
                StreamQuality(
                    quality="origin",
                    url=stream_url,
                    format=StreamType.FLV
                )
            )
        
        return stream_qualities
    
    def _parse_viewer_count(self, count_str: str) -> int:
        """解析观看人数字符串"""
        if isinstance(count_str, int):
            return count_str
        
        if not isinstance(count_str, str):
            return 0
        
        count_str = count_str.strip().lower()
        
        try:
            # 处理 k/w 后缀
            if count_str.endswith('k'):
                return int(float(count_str[:-1]) * 1000)
            elif count_str.endswith('w'):
                return int(float(count_str[:-1]) * 10000)
            else:
                return int(count_str)
        except (ValueError, TypeError):
            self.logger.warning(f"无法解析观看人数", count_str=count_str)
            return 0
    
    def _parse_timestamp(self, timestamp: Any) -> Optional[datetime]:
        """解析时间戳"""
        if not timestamp:
            return None
        
        try:
            # 尝试解析Unix时间戳
            if isinstance(timestamp, (int, float)):
                return datetime.fromtimestamp(timestamp)
            
            # 尝试解析ISO格式字符串
            if isinstance(timestamp, str):
                return datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                
        except Exception as e:
            self.logger.warning(f"无法解析时间戳", timestamp=timestamp, error=str(e))
        
        return None
    
    # ========== Swag平台特定方法 ==========
    
    async def search_streamers(self, keyword: str, page: int = 1, size: int = 20) -> List[UnifiedStreamer]:
        """
        搜索主播 - Swag平台特定功能
        
        Args:
            keyword: 搜索关键词
            page: 页码
            size: 每页大小
            
        Returns:
            主播列表
        """
        response = await self.raw_request(
            "GET",
            "/search/anchor",
            params={
                "keyword": keyword,
                "page": page,
                "limit": size
            }
        )
        
        items = self._extract_items_from_response(response)
        return [self.parse_streamer(item) for item in items]
    
    async def get_categories(self) -> List[Dict[str, str]]:
        """
        获取分类列表 - Swag平台特定功能
        
        Returns:
            分类列表
        """
        # Swag的分类是固定的，直接返回
        return [
            {"id": "hot", "name": "热门", "name_en": "Hot"},
            {"id": "new", "name": "新人", "name_en": "New"},
            {"id": "taiwan", "name": "台湾", "name_en": "Taiwan"},
            {"id": "korea", "name": "韩国", "name_en": "Korea"},
            {"id": "japan", "name": "日本", "name_en": "Japan"},
            {"id": "europe", "name": "欧美", "name_en": "Europe"}
        ] 