"""
Swag平台适配器测试
"""

import pytest
import pytest_asyncio
from unittest.mock import MagicMock

import sys
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from live_streams_core import Platform
from live_streams_core.platforms.swag import SwagAdapter


@pytest.fixture
def mock_response_list():
    """模拟直播列表响应"""
    return {
        "code": 200,
        "data": {
            "list": [
                {
                    "userId": "123456",
                    "roomId": "room123",
                    "username": "test_user",
                    "nickname": "测试主播",
                    "avatar": "https://example.com/avatar.jpg",
                    "cover": "https://example.com/cover.jpg",
                    "status": 1,
                    "title": "测试直播间",
                    "viewerCount": "1.2k",
                    "startTime": 1704038400,
                    "category": "hot",
                    "tags": ["美女", "才艺"],
                    "streamUrl": "https://example.com/stream.flv",
                    "isVip": False,
                    "needPassword": False,
                    "country": "Taiwan",
                    "city": "Taipei",
                    "age": 25,
                    "followerCount": 5000,
                    "level": 10
                },
                {
                    "userId": "789012",
                    "roomId": "room789",
                    "username": "offline_user",
                    "nickname": "离线主播",
                    "avatar": "https://example.com/avatar2.jpg",
                    "cover": "https://example.com/cover2.jpg",
                    "status": 0,
                    "title": "暂未开播",
                    "viewerCount": 0,
                    "category": "new",
                    "tags": [],
                    "isVip": True,
                    "needPassword": True
                }
            ],
            "total": 100
        }
    }


@pytest.fixture
def mock_response_room():
    """模拟房间详情响应"""
    return {
        "code": 200,
        "data": {
            "userId": "123456",
            "roomId": "room123",
            "status": 1,
            "streamUrl": "https://example.com/live/stream.flv",
            "title": "测试直播"
        }
    }


@pytest.mark.asyncio
async def test_swag_adapter_init():
    """测试适配器初始化"""
    adapter = SwagAdapter()
    
    assert adapter.platform == Platform.SWAG
    assert adapter.base_url == "https://capi.swag.live"
    assert adapter.http_client is not None


@pytest.mark.asyncio
async def test_parse_streamer(mock_response_list):
    """测试解析主播数据"""
    adapter = SwagAdapter()
    
    raw_data = mock_response_list["data"]["list"][0]
    streamer = adapter.parse_streamer(raw_data)
    
    # 验证基本字段
    assert streamer.id == "swag_123456"
    assert streamer.platform == Platform.SWAG
    assert streamer.platform_user_id == "123456"
    assert streamer.platform_room_id == "room123"
    assert streamer.username == "test_user"
    assert streamer.nickname == "测试主播"
    assert streamer.is_live is True
    assert streamer.title == "测试直播间"
    assert streamer.viewer_count == 1200  # "1.2k" -> 1200
    
    # 验证流地址
    assert len(streamer.stream_qualities) == 1
    assert streamer.stream_qualities[0].url == "https://example.com/stream.flv"
    assert streamer.stream_qualities[0].quality == "origin"
    
    # 验证额外字段
    assert streamer.extra_fields["country"] == "Taiwan"
    assert streamer.extra_fields["level"] == 10


@pytest.mark.asyncio
async def test_parse_offline_streamer(mock_response_list):
    """测试解析离线主播数据"""
    adapter = SwagAdapter()
    
    raw_data = mock_response_list["data"]["list"][1]
    streamer = adapter.parse_streamer(raw_data)
    
    assert streamer.is_live is False
    assert len(streamer.stream_qualities) == 0
    assert streamer.is_vip is True
    assert streamer.need_password is True


@pytest.mark.asyncio
async def test_get_live_list_with_mock(httpx_mock, mock_response_list):
    """测试获取直播列表（使用mock）"""
    httpx_mock.add_response(
        url="https://capi.swag.live/home/<USER>/list?sort=hot&page=1&limit=20",
        json=mock_response_list
    )
    
    adapter = SwagAdapter()
    streamers = await adapter.get_live_list()
    
    assert len(streamers) == 2
    assert streamers[0].nickname == "测试主播"
    assert streamers[1].nickname == "离线主播"


@pytest.mark.asyncio
async def test_get_stream_url_with_mock(httpx_mock, mock_response_room):
    """测试获取流地址（使用mock）"""
    httpx_mock.add_response(
        url="https://capi.swag.live/room/info/room123",
        json=mock_response_room
    )
    
    adapter = SwagAdapter()
    stream_qualities = await adapter.get_stream_url("room123")
    
    assert len(stream_qualities) == 1
    assert stream_qualities[0].url == "https://example.com/live/stream.flv"


@pytest.mark.asyncio
async def test_parse_viewer_count():
    """测试解析观看人数"""
    adapter = SwagAdapter()
    
    # 测试各种格式
    assert adapter._parse_viewer_count("1.2k") == 1200
    assert adapter._parse_viewer_count("5.5K") == 5500
    assert adapter._parse_viewer_count("10w") == 100000
    assert adapter._parse_viewer_count("2.5W") == 25000
    assert adapter._parse_viewer_count("999") == 999
    assert adapter._parse_viewer_count(888) == 888
    assert adapter._parse_viewer_count("invalid") == 0
    assert adapter._parse_viewer_count(None) == 0


@pytest.mark.asyncio
async def test_get_categories():
    """测试获取分类列表"""
    adapter = SwagAdapter()
    categories = await adapter.get_categories()
    
    assert len(categories) == 6
    assert any(cat["id"] == "hot" for cat in categories)
    assert any(cat["name"] == "台湾" for cat in categories)


@pytest.mark.asyncio
async def test_search_streamers_with_mock(httpx_mock):
    """测试搜索功能"""
    search_response = {
        "code": 200,
        "data": {
            "list": [
                {
                    "userId": "999",
                    "username": "beauty",
                    "nickname": "美女主播",
                    "status": 1
                }
            ]
        }
    }
    
    httpx_mock.add_response(
        url="https://capi.swag.live/search/anchor?keyword=%E7%BE%8E&page=1&limit=20",
        json=search_response
    )
    
    adapter = SwagAdapter()
    results = await adapter.search_streamers("美")
    
    assert len(results) == 1
    assert results[0].nickname == "美女主播"


if __name__ == "__main__":
    pytest.main([__file__, "-v"]) 