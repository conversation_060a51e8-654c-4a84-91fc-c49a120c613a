# 直播聚合核心库设计方案

## 1. 项目架构

### 1.1 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    Live Streams Core Library                 │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────┐  ┌──────────────┐  ┌──────────────────┐ │
│  │   Core      │  │  Platforms   │  │     Utils        │ │
│  │             │  │              │  │                  │ │
│  │ - Models    │  │ - Fanqie     │  │ - <PERSON>pt<PERSON>        │ │
│  │ - Base      │  │ - Lvcha      │  │ - HTTP Client   │ │
│  │ - Manager   │  │ - <PERSON><PERSON>      │  │ - <PERSON><PERSON>        │ │
│  │ - Response  │  │ - Stripchat  │  │ - Concurrency   │ │
│  │ - Exception │  │ - Swag       │  │                  │ │
│  └─────────────┘  │ - <PERSON><PERSON><PERSON>   │  └──────────────────┘ │
│                   └──────────────┘                        │
└─────────────────────────────────────────────────────────────┘
```

### 1.2 核心设计理念

1. **双模式数据访问**：既支持统一格式，也支持原始数据
2. **平台特定功能**：每个平台可以提供自己独特的API
3. **类型安全**：充分利用Python的类型系统
4. **异步优先**：所有IO操作都是异步的
5. **简洁高效**：避免过度设计，保持代码简洁

## 2. 数据模型设计

### 2.1 统一数据模型

```python
from pydantic import BaseModel, Field, ConfigDict
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum

class Platform(str, Enum):
    """支持的平台"""
    FANQIE = "fanqie"
    LVCHA = "lvcha"
    PANDA = "panda"
    STRIPCHAT = "stripchat"
    SWAG = "swag"
    XIAOHULI = "xiaohuli"

class StreamQuality(BaseModel):
    """流质量信息"""
    quality: str = Field(..., description="画质标识")
    url: str = Field(..., description="流地址")
    format: Optional[str] = Field(None, description="流格式: flv/hls/rtmp")
    bitrate: Optional[int] = Field(None, description="码率")
    resolution: Optional[str] = Field(None, description="分辨率")

class UnifiedStreamer(BaseModel):
    """统一的主播数据模型"""
    model_config = ConfigDict(extra="allow")  # 允许额外字段
    
    # 核心字段（所有平台都有）
    id: str = Field(..., description="唯一标识: platform_userid")
    platform: Platform
    platform_user_id: str = Field(..., description="平台用户ID")
    platform_room_id: Optional[str] = Field(None, description="平台房间ID")
    
    # 基本信息
    username: str = Field(..., description="用户名")
    nickname: str = Field(..., description="昵称")
    avatar_url: Optional[str] = None
    cover_url: Optional[str] = None
    
    # 直播状态
    is_live: bool = False
    title: Optional[str] = None
    viewer_count: int = Field(default=0, ge=0)
    start_time: Optional[datetime] = None
    category: Optional[str] = None
    tags: List[str] = Field(default_factory=list)
    
    # 流信息
    stream_qualities: List[StreamQuality] = Field(default_factory=list)
    
    # 访问限制
    is_vip: bool = False
    need_password: bool = False
    need_login: bool = False
    
    # 原始数据和扩展字段
    raw_data: Optional[Dict[str, Any]] = Field(
        None, 
        description="原始API响应数据"
    )
    extra_fields: Dict[str, Any] = Field(
        default_factory=dict,
        description="平台特定的额外字段"
    )
    
    # 元数据
    last_updated: datetime = Field(default_factory=datetime.now)
    
    def get_best_stream_url(self) -> Optional[str]:
        """获取最佳质量的流地址"""
        if not self.stream_qualities:
            return None
        
        # 优先级: origin > 1080p > 720p > 480p
        priority_map = {
            "origin": 0, "原画": 0,
            "1080p": 1, "超清": 1,
            "720p": 2, "高清": 2,
            "480p": 3, "标清": 3
        }
        
        sorted_qualities = sorted(
            self.stream_qualities,
            key=lambda x: priority_map.get(x.quality.lower(), 999)
        )
        return sorted_qualities[0].url if sorted_qualities else None
```

### 2.2 响应包装器

```python
from typing import TypeVar, Generic, List

T = TypeVar('T', bound=BaseModel)

class ResponseWrapper(BaseModel, Generic[T]):
    """单个数据的响应包装器"""
    data: T  # 统一格式的数据
    raw: Dict[str, Any]  # 原始响应数据
    platform: str  # 平台标识
    timestamp: datetime = Field(default_factory=datetime.now)

class ListResponse(BaseModel, Generic[T]):
    """列表数据的响应包装器"""
    items: List[T]  # 统一格式的列表
    raw_items: List[Dict[str, Any]]  # 原始数据列表
    total: Optional[int] = None  # 总数
    page: Optional[int] = None  # 当前页
    page_size: Optional[int] = None  # 每页大小
    has_more: bool = False  # 是否有更多数据
    platform: str  # 平台标识
    timestamp: datetime = Field(default_factory=datetime.now)
    
    def get_raw_by_index(self, index: int) -> Optional[Dict[str, Any]]:
        """根据索引获取原始数据"""
        if 0 <= index < len(self.raw_items):
            return self.raw_items[index]
        return None
```

## 3. 平台适配器设计

### 3.1 基类设计

```python
from abc import ABC, abstractmethod
from typing import List, Dict, Optional, Any
from loguru import logger
from ..utils.http_client import AsyncHTTPClient
from .models import Platform, UnifiedStreamer, StreamQuality, ListResponse
from .exceptions import PlatformException

class PlatformAdapter(ABC):
    """平台适配器基类 - 支持统一接口和原始数据访问"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.platform = self._get_platform()
        self.logger = logger.bind(platform=self.platform.value)
        self.base_url = self._get_base_url()
        self.http_client: Optional[AsyncHTTPClient] = None
        self._setup()
    
    def _setup(self):
        """初始化设置"""
        self.http_client = AsyncHTTPClient(
            base_url=self.base_url,
            headers=self._build_headers(),
            timeout=self.config.get("timeout", 30.0),
            proxy=self.config.get("proxy"),
            http2=self.config.get("http2", True)
        )
    
    @abstractmethod
    def _get_platform(self) -> Platform:
        """获取平台标识"""
        pass
    
    @abstractmethod
    def _get_base_url(self) -> str:
        """获取基础URL"""
        pass
    
    @abstractmethod
    def _build_headers(self) -> Dict[str, str]:
        """构建请求头"""
        return {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "Accept": "application/json",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8"
        }
    
    # ========== 统一接口 ==========
    
    @abstractmethod
    async def get_live_list(
        self, 
        category: str = "hot",
        page: int = 1,
        size: int = 20,
        **kwargs
    ) -> List[UnifiedStreamer]:
        """获取直播列表 - 只返回统一格式"""
        pass
    
    async def get_live_list_with_raw(
        self,
        category: str = "hot",
        page: int = 1,
        size: int = 20,
        **kwargs
    ) -> ListResponse[UnifiedStreamer]:
        """获取直播列表 - 返回统一格式和原始数据"""
        # 获取原始数据
        raw_response = await self._fetch_live_list_raw(
            category=category,
            page=page,
            size=size,
            **kwargs
        )
        
        # 解析数据
        raw_items = self._extract_items_from_response(raw_response)
        streamers = [self.parse_streamer(item) for item in raw_items]
        
        # 构建响应
        return ListResponse(
            items=streamers,
            raw_items=raw_items,
            total=self._extract_total_from_response(raw_response),
            page=page,
            page_size=size,
            has_more=self._check_has_more(raw_response, page, size),
            platform=self.platform.value
        )
    
    @abstractmethod
    async def get_stream_url(self, room_id: str) -> List[StreamQuality]:
        """获取直播流地址"""
        pass
    
    @abstractmethod
    def parse_streamer(self, raw_data: Dict[str, Any]) -> UnifiedStreamer:
        """解析原始数据为统一格式"""
        pass
    
    # ========== 原始数据访问 ==========
    
    async def raw_request(
        self,
        method: str,
        endpoint: str,
        **kwargs
    ) -> Dict[str, Any]:
        """通用的原始请求方法"""
        self.logger.debug(f"原始请求", method=method, endpoint=endpoint)
        
        url = endpoint if endpoint.startswith("http") else f"{self.base_url}/{endpoint}"
        
        try:
            response = await self.http_client.request(method, url, **kwargs)
            response.raise_for_status()
            data = response.json()
            
            self.logger.debug(f"原始响应", status=response.status_code)
            return data
            
        except Exception as e:
            self.logger.error(f"原始请求失败", error=str(e))
            raise
    
    @abstractmethod
    async def _fetch_live_list_raw(
        self,
        category: str,
        page: int,
        size: int,
        **kwargs
    ) -> Dict[str, Any]:
        """获取直播列表的原始响应"""
        pass
    
    def _extract_items_from_response(self, response: Dict[str, Any]) -> List[Dict[str, Any]]:
        """从响应中提取数据项列表"""
        # 默认实现，子类可以覆盖
        if "data" in response and isinstance(response["data"], list):
            return response["data"]
        elif "list" in response:
            return response["list"]
        elif "items" in response:
            return response["items"]
        return []
    
    def _extract_total_from_response(self, response: Dict[str, Any]) -> Optional[int]:
        """从响应中提取总数"""
        # 默认实现，子类可以覆盖
        if "total" in response:
            return response["total"]
        elif "totalCount" in response:
            return response["totalCount"]
        return None
    
    def _check_has_more(self, response: Dict[str, Any], page: int, size: int) -> bool:
        """检查是否有更多数据"""
        # 默认实现，子类可以覆盖
        if "hasMore" in response:
            return response["hasMore"]
        elif "has_more" in response:
            return response["has_more"]
        
        # 基于total计算
        total = self._extract_total_from_response(response)
        if total is not None:
            return page * size < total
        
        # 基于返回数据量判断
        items = self._extract_items_from_response(response)
        return len(items) >= size
    
    # ========== 辅助方法 ==========
    
    def _build_unified_id(self, user_id: str) -> str:
        """构建统一ID"""
        return f"{self.platform.value}_{user_id}"
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.http_client:
            await self.http_client.close()
```

### 3.2 平台特定功能示例

```python
# 番茄平台适配器 - 包含特定功能
class FanqieAdapter(PlatformAdapter):
    """番茄平台适配器"""
    
    def _get_platform(self) -> Platform:
        return Platform.FANQIE
    
    def _get_base_url(self) -> str:
        return "https://api.example.com"
    
    # ========== 统一接口实现 ==========
    
    async def get_live_list(self, **kwargs) -> List[UnifiedStreamer]:
        """实现统一接口"""
        response = await self.get_live_list_with_raw(**kwargs)
        return response.items
    
    # ========== 平台特定功能 ==========
    
    async def get_fans_group(self, room_id: str) -> Dict[str, Any]:
        """获取粉丝团信息 - 番茄平台特有"""
        self.logger.info(f"获取粉丝团信息", room_id=room_id)
        
        endpoint = f"fans/fansClub/{room_id}"
        raw_data = await self.raw_request("GET", endpoint)
        
        # 处理加密响应
        if "data" in raw_data and isinstance(raw_data["data"], str):
            decrypted = self._decrypt_response(raw_data["data"])
            raw_data["data"] = json.loads(decrypted)
        
        return raw_data
    
    async def get_user_detail(self, user_id: str) -> Dict[str, Any]:
        """获取用户详细信息 - 番茄平台特有"""
        self.logger.info(f"获取用户详情", user_id=user_id)
        
        data = {
            "userId": user_id,
            "requestId": str(uuid.uuid4())
        }
        
        # 加密请求
        encrypted_data = self._encrypt_request(data)
        
        return await self.raw_request(
            "POST",
            "user/detail",
            json={"data": encrypted_data}
        )
    
    async def get_room_extra_info(self, room_id: str) -> Dict[str, Any]:
        """获取房间额外信息 - 番茄平台特有"""
        # 实现平台特定的房间信息获取
        pass
    
    # ========== 加密相关 ==========
    
    def _encrypt_request(self, data: Dict[str, Any]) -> str:
        """番茄平台特有的请求加密"""
        json_str = json.dumps(data, separators=(',', ':'))
        xor_result = self._xor_encrypt(json_str)
        base64_result = base64.b64encode(xor_result.encode()).decode()
        return binascii.hexlify(base64_result.encode()).decode()
    
    def _decrypt_response(self, encrypted: str) -> str:
        """番茄平台特有的响应解密"""
        # 实现解密逻辑
        pass

# 小狐狸平台适配器 - 包含特定功能
class XiaohuliAdapter(PlatformAdapter):
    """小狐狸平台适配器"""
    
    # ========== 平台特定功能 ==========
    
    async def get_ranking_list(
        self,
        rank_type: str = "charm",
        period: str = "daily"
    ) -> Dict[str, Any]:
        """获取排行榜 - 小狐狸平台特有"""
        self.logger.info(f"获取排行榜", type=rank_type, period=period)
        
        params = {
            "type": rank_type,
            "period": period
        }
        
        return await self.raw_request("GET", "ranking/list", params=params)
    
    async def get_system_config(self) -> Dict[str, Any]:
        """获取系统配置 - 小狐狸平台特有"""
        self.logger.info("获取系统配置")
        
        response = await self.raw_request("GET", "system/config")
        
        # 解密配置数据
        if "data" in response and isinstance(response["data"], str):
            decrypted = self._decrypt_with_token(response["data"])
            response["data"] = json.loads(decrypted)
        
        return response
    
    async def user_operation(
        self,
        action: str,
        target_id: Optional[str] = None,
        **params
    ) -> Dict[str, Any]:
        """用户操作接口 - 小狐狸平台特有"""
        self.logger.info(f"执行用户操作", action=action)
        
        data = {
            "action": action,
            "timestamp": int(time.time() * 1000)
        }
        
        if target_id:
            data["targetId"] = target_id
        
        data.update(params)
        
        return await self.raw_request("POST", "user/operation", json=data)
    
    def _decrypt_with_token(self, encrypted: str) -> str:
        """使用token作为密钥解密"""
        # 实现基于token的解密
        pass
```

## 4. 认证管理

### 设计原则
1. **调用者负责**: 认证信息由调用者管理，库只使用不存储
2. **平台独立**: 每个平台有自己的认证机制和参数
3. **透明传递**: 认证参数通过config传入，适配器透明使用

### 认证方式

#### 1. 不需要认证的平台
```python
# Swag平台 - 直接使用
adapter = SwagAdapter()
streamers = await adapter.get_live_list()
```

#### 2. 需要认证的平台
```python
# 小狐狸平台 - 需要JWT和解密token
adapter = XiaohuliAdapter(config={
    "jwt_token": "your_jwt_token",
    "token": "decrypt_token",
    "x_live_butter2": "optional_param"
})
```

### 获取认证信息

#### 静态方法登录
```python
# 游客登录
login_data = await XiaohuliAdapter.guest_login_static()
jwt_token = login_data["jwt_token"]
token = login_data["token"]

# 账号登录
captcha_data = await XiaohuliAdapter.get_login_captcha_static()
login_data = await XiaohuliAdapter.login_static(
    username="user",
    password="pass", 
    captcha="1234",
    captcha_id=captcha_data["captcha_id"]
)
```

### 认证信息管理示例
```python
class AuthManager:
    """认证信息管理器"""
    
    def save_platform_auth(self, platform: str, auth_info: Dict):
        """保存平台认证信息"""
        self.auth_data[platform] = {
            **auth_info,
            "updated_at": datetime.now().isoformat()
        }
        
    def is_auth_expired(self, platform: str, expire_days: int = 30):
        """检查认证是否过期"""
        # 检查更新时间
```

### 各平台认证要求

| 平台 | 认证方式 | 必需参数 | 有效期 | 备注 |
|------|---------|---------|--------|------|
| Swag | 无 | - | - | 开放API |
| 小狐狸 | JWT | jwt_token, token | 30天 | 支持游客/账号登录 |
| 番茄 | 签名 | device_id, sign | - | 复杂加密算法 |
| 绿茶 | Token | auth_token | - | AES加密 |
| 熊猫 | Cookie | session_id | - | 需要代理 |
| Stripchat | API Key | api_key | - | 可选认证 |

## 5. 使用示例

### 4.1 基础使用

```python
import asyncio
from live_streams_core import PlatformManager, Platform

async def basic_usage():
    manager = PlatformManager()
    
    # 方式1：只获取统一格式
    adapter = manager.get_adapter(Platform.SWAG)
    streamers = await adapter.get_live_list(page=1, size=20)
    
    for streamer in streamers:
        print(f"{streamer.nickname}: {streamer.viewer_count} 观看")
        print(f"最佳流地址: {streamer.get_best_stream_url()}")

asyncio.run(basic_usage())
```

### 4.2 访问原始数据

```python
async def raw_data_usage():
    adapter = manager.get_adapter(Platform.FANQIE)
    
    # 获取统一格式和原始数据
    response = await adapter.get_live_list_with_raw(category="hot", page=1)
    
    print(f"总数: {response.total}")
    print(f"是否有更多: {response.has_more}")
    
    # 同时访问统一格式和原始数据
    for i, streamer in enumerate(response.items):
        print(f"\n主播: {streamer.nickname}")
        print(f"统一ID: {streamer.id}")
        print(f"观看人数: {streamer.viewer_count}")
        
        # 访问原始数据
        raw = response.raw_items[i]
        print(f"原始数据中的特殊字段: {raw.get('special_field')}")
        
        # 或者通过streamer直接访问
        if streamer.raw_data:
            print(f"原始标题: {streamer.raw_data.get('original_title')}")
```

### 4.3 使用平台特定功能

```python
async def platform_specific_usage():
    adapter = manager.get_adapter(Platform.FANQIE)
    
    # 类型检查确保是番茄平台
    if isinstance(adapter, FanqieAdapter):
        # 使用番茄平台特有功能
        fans_info = await adapter.get_fans_group("room123")
        print(f"粉丝团信息: {fans_info}")
        
        user_detail = await adapter.get_user_detail("user456")
        print(f"用户详情: {user_detail}")
    
    # 小狐狸平台特定功能
    xiaohuli = manager.get_adapter(Platform.XIAOHULI)
    if isinstance(xiaohuli, XiaohuliAdapter):
        # 获取排行榜
        ranking = await xiaohuli.get_ranking_list(rank_type="gift")
        print(f"礼物排行榜: {ranking}")
        
        # 系统配置
        config = await xiaohuli.get_system_config()
        print(f"系统配置: {config}")
```

### 4.4 通用原始请求

```python
async def generic_raw_request():
    adapter = manager.get_adapter(Platform.SWAG)
    
    # 使用通用的原始请求方法访问任意端点
    custom_data = await adapter.raw_request(
        "GET",
        "custom/endpoint",
        params={"filter": "special"}
    )
    
    print(f"自定义端点响应: {custom_data}")
```

### 4.5 并发请求多平台

```python
async def concurrent_platforms():
    # 定义一个通用函数来获取平台数据
    async def get_platform_data(platform: Platform):
        adapter = manager.get_adapter(platform)
        
        # 同时获取统一数据和平台特定数据
        tasks = [adapter.get_live_list_with_raw()]
        
        # 根据平台类型添加特定请求
        if isinstance(adapter, FanqieAdapter):
            tasks.append(adapter.get_fans_group("popular_room"))
        elif isinstance(adapter, XiaohuliAdapter):
            tasks.append(adapter.get_ranking_list())
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        return platform, results
    
    # 并发请求多个平台
    platforms = [Platform.FANQIE, Platform.XIAOHULI, Platform.SWAG]
    results = await asyncio.gather(
        *[get_platform_data(p) for p in platforms],
        return_exceptions=True
    )
    
    for platform, data in results:
        if isinstance(data, Exception):
            print(f"{platform.value} 错误: {data}")
        else:
            print(f"{platform.value} 成功获取数据")
```

## 6. 错误处理

```python
from live_streams_core.exceptions import (
    PlatformException,
    AuthenticationError,
    RateLimitError,
    NetworkException
)

async def error_handling_example():
    adapter = manager.get_adapter(Platform.FANQIE)
    
    try:
        streamers = await adapter.get_live_list()
        
    except AuthenticationError:
        # 认证失败，可能需要更新token
        logger.error("认证失败，请检查配置")
        
    except RateLimitError as e:
        # 频率限制，等待后重试
        logger.warning(f"触发频率限制: {e}")
        await asyncio.sleep(60)
        
    except NetworkException as e:
        # 网络错误
        logger.error(f"网络错误: {e}")
        
    except PlatformException as e:
        # 平台通用错误
        logger.error(f"平台错误: {e}")
```

## 7. 最佳实践

### 6.1 配置管理

```python
# 使用环境变量管理敏感配置
import os
from dotenv import load_dotenv

load_dotenv()

config = {
    "fanqie": {
        "auth_token": os.getenv("FANQIE_TOKEN"),
        "timeout": 30,
        "http2": True
    },
    "xiaohuli": {
        "username": os.getenv("XIAOHULI_USER"),
        "password": os.getenv("XIAOHULI_PASS"),
        "proxy": os.getenv("PROXY_URL")
    }
}

manager = PlatformManager(config)
```

### 6.2 日志管理

```python
from live_streams_core.utils.logger import setup_logger

# 配置日志
setup_logger(
    log_level="INFO",
    log_dir="logs",
    rotation="100 MB",
    retention="7 days"
)

# 使用上下文信息
logger.bind(user_id="123").info("用户操作")
```

### 6.3 资源管理

```python
# 使用异步上下文管理器
async with manager.get_adapter(Platform.SWAG) as adapter:
    streamers = await adapter.get_live_list()
    # 自动清理资源
```

## 8. 性能优化

### 7.1 并发控制

```python
from live_streams_core.utils.concurrency import ConcurrencyManager

async def controlled_concurrent():
    concurrency = ConcurrencyManager(max_concurrent=5)
    
    # 控制并发数的批量请求
    room_ids = ["room1", "room2", "room3", ...]
    
    async def get_stream_url(room_id: str):
        return await adapter.get_stream_url(room_id)
    
    # 限制同时只有5个请求
    results = await concurrency.map_with_limit(
        get_stream_url,
        room_ids
    )
```

### 7.2 连接复用

```python
# httpx会自动复用连接
# 确保复用同一个adapter实例
adapter = manager.get_adapter(Platform.FANQIE)

# 多次请求会复用连接
for i in range(10):
    await adapter.get_live_list(page=i+1)
```

## 9. 扩展指南

### 8.1 添加新平台

1. 创建新的适配器文件 `platforms/newplatform.py`
2. 继承 `PlatformAdapter` 基类
3. 实现必需的抽象方法
4. 添加平台特定功能（可选）
5. 在 `Platform` 枚举中添加新平台
6. 在 `PlatformManager` 中注册

### 8.2 添加平台特定功能

```python
class NewPlatformAdapter(PlatformAdapter):
    # 实现基础功能...
    
    # 添加平台特定功能
    async def special_feature(self, param: str) -> Dict[str, Any]:
        """平台特有的功能"""
        return await self.raw_request("POST", "special/api", json={"param": param})
```

## 10. 架构优势

1. **灵活性**：既有统一接口，又能访问原始数据和平台特定功能
2. **类型安全**：充分利用Python类型系统，IDE友好
3. **可扩展**：轻松添加新平台和新功能
4. **高性能**：基于httpx和异步IO
5. **易于测试**：清晰的接口定义，便于mock
6. **代码复用**：基类提供通用功能，减少重复代码 