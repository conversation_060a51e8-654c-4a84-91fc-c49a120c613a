"""
直播聚合核心库 - HTTP客户端

基于 httpx 的现代化异步 HTTP 客户端
"""

import asyncio
from typing import Any, Dict, Optional

import httpx
from loguru import logger

from ..core.exceptions import NetworkException, TimeoutError


class AsyncHTTPClient:
    """基于httpx的现代化异步HTTP客户端"""
    
    def __init__(
        self,
        base_url: Optional[str] = None,
        headers: Optional[Dict[str, str]] = None,
        timeout: float = 30.0,
        max_connections: int = 100,
        proxy: Optional[str] = None,
        http2: bool = True,
        follow_redirects: bool = True,
        max_retries: int = 3,
        retry_delay: float = 1.0
    ):
        """
        初始化HTTP客户端
        
        Args:
            base_url: 基础URL
            headers: 默认请求头
            timeout: 超时时间（秒）
            max_connections: 最大连接数
            proxy: 代理地址
            http2: 是否启用HTTP/2
            follow_redirects: 是否跟随重定向
            max_retries: 最大重试次数
            retry_delay: 重试延迟（秒）
        """
        # 连接池限制
        limits = httpx.Limits(
            max_keepalive_connections=max_connections,
            max_connections=max_connections,
            keepalive_expiry=30.0
        )
        
        # 创建客户端
        self.client = httpx.AsyncClient(
            base_url=base_url,
            headers=headers or {},
            timeout=httpx.Timeout(timeout),
            limits=limits,
            http2=http2,
            proxy=proxy,
            follow_redirects=follow_redirects
        )
        
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        
        logger.debug(
            "HTTP客户端初始化",
            base_url=base_url,
            http2=http2,
            proxy=proxy
        )
    
    async def request(
        self,
        method: str,
        url: str,
        retry: bool = True,
        **kwargs
    ) -> httpx.Response:
        """
        发送HTTP请求
        
        Args:
            method: HTTP方法
            url: 请求URL
            retry: 是否重试
            **kwargs: 其他httpx请求参数
            
        Returns:
            HTTP响应对象
            
        Raises:
            TimeoutError: 请求超时
            NetworkException: 网络错误
        """
        last_error = None
        retries = self.max_retries if retry else 1
        
        for attempt in range(retries):
            try:
                logger.debug(
                    f"发送请求",
                    method=method,
                    url=url,
                    attempt=attempt + 1,
                    max_attempts=retries
                )
                
                response = await self.client.request(method, url, **kwargs)
                
                # 检查状态码
                response.raise_for_status()
                
                logger.debug(
                    f"请求成功",
                    status=response.status_code,
                    content_length=len(response.content)
                )
                
                return response
                
            except httpx.TimeoutException as e:
                last_error = TimeoutError(f"请求超时: {url}")
                logger.warning(
                    f"请求超时",
                    url=url,
                    attempt=attempt + 1,
                    error=str(e)
                )
                
            except httpx.HTTPStatusError as e:
                # 服务器错误（5xx）才重试
                if e.response.status_code >= 500 and attempt < retries - 1:
                    last_error = NetworkException(f"服务器错误: {e.response.status_code}")
                    logger.warning(
                        f"服务器错误",
                        status=e.response.status_code,
                        url=url,
                        attempt=attempt + 1
                    )
                else:
                    # 客户端错误（4xx）直接抛出
                    logger.error(
                        f"HTTP错误",
                        status=e.response.status_code,
                        url=url,
                        response_text=e.response.text[:500]  # 只记录前500字符
                    )
                    raise NetworkException(f"HTTP {e.response.status_code}: {e.response.text[:200]}")
                    
            except httpx.NetworkError as e:
                last_error = NetworkException(f"网络错误: {str(e)}")
                logger.error(
                    f"网络错误",
                    error=str(e),
                    url=url,
                    attempt=attempt + 1
                )
                
            except Exception as e:
                last_error = NetworkException(f"未知错误: {str(e)}")
                logger.error(
                    f"请求异常",
                    error=str(e),
                    url=url,
                    attempt=attempt + 1,
                    exc_info=True
                )
            
            # 如果还有重试机会，等待后重试
            if attempt < retries - 1:
                delay = self.retry_delay * (attempt + 1)
                logger.debug(f"等待重试", delay=delay)
                await asyncio.sleep(delay)
        
        # 所有重试都失败了
        if last_error:
            raise last_error
        else:
            raise NetworkException("请求失败")
    
    async def get(self, url: str, **kwargs) -> httpx.Response:
        """GET请求"""
        return await self.request("GET", url, **kwargs)
    
    async def post(self, url: str, **kwargs) -> httpx.Response:
        """POST请求"""
        return await self.request("POST", url, **kwargs)
    
    async def put(self, url: str, **kwargs) -> httpx.Response:
        """PUT请求"""
        return await self.request("PUT", url, **kwargs)
    
    async def delete(self, url: str, **kwargs) -> httpx.Response:
        """DELETE请求"""
        return await self.request("DELETE", url, **kwargs)
    
    async def head(self, url: str, **kwargs) -> httpx.Response:
        """HEAD请求"""
        return await self.request("HEAD", url, **kwargs)
    
    async def options(self, url: str, **kwargs) -> httpx.Response:
        """OPTIONS请求"""
        return await self.request("OPTIONS", url, **kwargs)
    
    async def close(self):
        """关闭客户端，释放资源"""
        await self.client.aclose()
        logger.debug("HTTP客户端已关闭")
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()


def create_client(**kwargs) -> AsyncHTTPClient:
    """
    创建HTTP客户端的工厂函数
    
    Args:
        **kwargs: AsyncHTTPClient初始化参数
        
    Returns:
        AsyncHTTPClient实例
    """
    return AsyncHTTPClient(**kwargs) 