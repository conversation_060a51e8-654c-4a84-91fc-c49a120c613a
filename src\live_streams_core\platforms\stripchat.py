"""
Stripchat平台适配器

Stripchat平台特点：
- 国际化成人直播平台
- 无需认证（公开API）
- HLS流媒体格式
- 支持多地区、多语言过滤
"""

from datetime import datetime
from typing import Any, Dict, List, Optional

from loguru import logger

from ..core.base import PlatformAdapter
from ..core.exceptions import PlatformException
from ..core.models import Platform, StreamQuality, StreamType, UnifiedStreamer


class StripchatAdapter(PlatformAdapter):
    """Stripchat平台适配器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化适配器
        
        Args:
            config: 配置信息（可选），包含以下参数:
                过滤参数（可选）:
                - language: 语言过滤（可选）
                - region: 地区过滤（可选）
                
                网络参数（可选）:
                - base_url: 自定义API域名（可选，默认: https://zh.stripchat.com）
        """
        super().__init__(config)
        
        # 从配置中获取过滤选项
        self.language = self.config.get("language", "")
        self.region = self.config.get("region", "")
        
        # Stripchat是公开API，无需认证
        self.is_authenticated = True
    
    def _get_platform(self) -> Platform:
        """获取平台标识"""
        return Platform.STRIPCHAT
    
    def _get_base_url(self) -> str:
        """获取基础URL"""
        # 支持自定义域名，如果配置中有base_url则使用，否则使用默认值
        return self.config.get("base_url", "https://zh.stripchat.com")
    
    def _build_headers(self) -> Dict[str, str]:
        """构建请求头"""
        return {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Accept": "application/json, text/plain, */*",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Accept-Encoding": "gzip, deflate, br",
            "Referer": "https://zh.stripchat.com/"
        }
    
    # ========== 统一接口实现 ==========
    
    async def get_live_list(
        self,
        category: str = "hot",
        page: int = 1,
        size: int = 20,
        **kwargs
    ) -> List[UnifiedStreamer]:
        """获取直播列表 - 只返回统一格式"""
        response = await self.get_live_list_with_raw(
            category=category,
            page=page,
            size=size,
            **kwargs
        )
        return response.items
    
    async def _fetch_live_list_raw(
        self,
        category: str,
        page: int,
        size: int,
        **kwargs
    ) -> Dict[str, Any]:
        """获取直播列表的原始响应"""
        # 计算偏移量
        offset = (page - 1) * size
        
        # 构建基础查询参数
        params = {
            "improveTs": "false",
            "removeShows": "false",
            "limit": min(size, 60),  # 最大60
            "offset": offset,
            "primaryTag": "girls",
            "rcmGrp": "A",
            "rbCnGr": "false"
        }
        
        # 根据分类设置过滤条件
        if category == "chinese":
            # 中文地区
            params.update({
                "filterGroupTags": '[["tagLanguageChinese"]]',
                "sortBy": "stripRanking",
                "parentTag": "tagLanguageChinese"
            })
        elif category == "japanese":
            # 日本地区
            params.update({
                "filterGroupTags": '[["tagLanguageJapanese"]]',
                "sortBy": "stripRanking",
                "parentTag": "tagLanguageChinese"
            })
        elif category == "new":
            # 新人主播
            params.update({
                "filterGroupTags": '[["autoTagNew"]]',
                "sortBy": "trending",
                "parentTag": "autoTagNew",
                "rcmGrp": "B"
            })
        elif category == "mobile":
            # 手机直播
            params.update({
                "filterGroupTags": '[["mobile"]]',
                "sortBy": "stripRanking",
                "parentTag": "mobile",
                "rcmGrp": "B"
            })
        elif category == "viewers":
            # 按观看人数排序
            params.update({
                "removeShows": "true",
                "sortBy": "viewersRating",
                "rcmGrp": "B"
            })
        else:
            # 默认热门排序
            params.update({
                "removeShows": "true",
                "sortBy": "viewersRating",
                "rcmGrp": "B"
            })
        
        # 发送请求
        response = await self.raw_request(
            "GET",
            "/api/front/models",
            params=params
        )
        
        # Stripchat的API总是返回成功，不需要额外检查
        return response
    
    def _extract_items_from_response(self, response: Dict[str, Any]) -> List[Dict[str, Any]]:
        """从响应中提取数据项列表"""
        return response.get("models", [])
    
    def _extract_total_from_response(self, response: Dict[str, Any]) -> Optional[int]:
        """从响应中提取总数"""
        return response.get("totalCount")
    
    def parse_streamer(self, raw_data: Dict[str, Any]) -> UnifiedStreamer:
        """解析原始数据为统一格式"""
        # 提取关键字段
        model_id = str(raw_data.get("id", ""))
        username = raw_data.get("username", "")
        
        # 解析直播状态
        is_live = raw_data.get("isLive", False)
        is_online = raw_data.get("isOnline", False)
        
        # 解析观看人数
        viewer_count = raw_data.get("viewersCount", 0)
        
        # 解析付费信息
        private_rate = raw_data.get("privateRate", 0)
        spy_rate = raw_data.get("spyRate", 0)
        is_vip = private_rate > 0 or spy_rate > 0
        
        # 解析开播时间
        start_time = None
        if raw_data.get("statusChangedAt"):
            try:
                start_time = datetime.fromisoformat(raw_data["statusChangedAt"].replace('Z', '+00:00'))
            except ValueError:
                pass
        
        # 构建头像和封面URL
        avatar_url = raw_data.get("avatarUrl", "")
        preview_url = raw_data.get("previewUrlThumbSmall", "")
        
        # 解析性别和分类
        gender = raw_data.get("gender", "")
        gender_group = raw_data.get("genderGroup", "")
        category = raw_data.get("broadcastGender", "")
        
        # 解析标签
        tags = []
        if raw_data.get("isNew"):
            tags.append("新主播")
        if raw_data.get("isHd"):
            tags.append("高清")
        if raw_data.get("isMobile"):
            tags.append("手机直播")
        if raw_data.get("isVr"):
            tags.append("VR")
        if raw_data.get("isLovense"):
            tags.append("Lovense")
        if raw_data.get("isKiiroo"):
            tags.append("Kiiroo")
        if raw_data.get("isNonNude"):
            tags.append("非裸体")
        if raw_data.get("isRecommended"):
            tags.append("推荐")
        if raw_data.get("isTagVerified"):
            tags.append("标签验证")
        
        # 添加性别标签
        if gender:
            tags.append(f"性别:{gender}")
        
        # 添加国家标签
        country = raw_data.get("country", "")
        if country:
            tags.append(f"国家:{country}")
        
        # 构建统一数据模型
        streamer = UnifiedStreamer(
            id=self._build_unified_id(username),
            platform=self.platform,
            platform_user_id=model_id,
            platform_room_id=model_id,
            username=username,
            nickname=username,
            avatar_url=avatar_url,
            cover_url=preview_url,
            is_live=is_live and is_online,
            title="",  # Stripchat没有房间标题
            viewer_count=viewer_count,
            start_time=start_time,
            category=category,
            tags=tags,
            stream_qualities=[],  # 流地址需要单独获取
            is_vip=is_vip,
            need_password=False,
            need_login=False,  # Stripchat可以不登录观看
            raw_data=raw_data
        )
        
        # 添加平台特定字段
        streamer.extra_fields.update({
            "model_id": model_id,
            "gender": gender,
            "gender_group": gender_group,
            "country": country,
            "status": raw_data.get("status", ""),
            "private_rate": private_rate,
            "spy_rate": spy_rate,
            "p2p_rate": raw_data.get("p2pRate", 0),
            "p2p_voice_rate": raw_data.get("p2pVoiceRate", 0),
            "public_recordings_rate": raw_data.get("publicRecordingsRate", 0),
            "top_best_place": raw_data.get("topBestPlace", 0),
            "do_spy": raw_data.get("doSpy", False),
            "do_private": raw_data.get("doPrivate", False),
            "is_avatar_approved": raw_data.get("isAvatarApproved", False),
            "stream_name": raw_data.get("streamName", ""),
            "presets": raw_data.get("presets", []),
            "broadcast_settings": raw_data.get("broadcastSettings", {}),
            "group_show_type": raw_data.get("groupShowType", ""),
            "group_show_topic": raw_data.get("groupShowTopic", ""),
            "has_group_show_announcement": raw_data.get("hasGroupShowAnnouncement", False),
            "snapshot_timestamp": raw_data.get("snapshotTimestamp", ""),
            "popular_snapshot_timestamp": raw_data.get("popularSnapshotTimestamp", 0)
        })
        
        return streamer
    
    async def get_stream_url(self, room_id: str) -> List[StreamQuality]:
        """获取直播流地址"""
        try:
            # 获取模型详细信息
            model_info = await self.get_model_info(room_id)
            
            stream_qualities = []
            
            # 提取HLS播放列表
            hls_playlist = model_info.get("hlsPlaylist", "")
            if hls_playlist:
                stream_qualities.append(
                    StreamQuality(
                        quality="origin",
                        url=hls_playlist,
                        format=StreamType.HLS
                    )
                )
            
            # 提取不同质量的预设
            presets = model_info.get("presets", [])
            for preset in presets:
                if preset != "origin" and hls_playlist:
                    # 根据预设构建不同质量的URL
                    quality_url = hls_playlist.replace("master/", f"{preset}/")
                    stream_qualities.append(
                        StreamQuality(
                            quality=preset,
                            url=quality_url,
                            format=StreamType.HLS
                        )
                    )
            
            return stream_qualities
            
        except Exception as e:
            self.logger.error("获取流地址失败", room_id=room_id, error=str(e))
            return []
    
    # ========== Stripchat平台特定方法 ==========
    
    async def get_model_info(self, model_id: str) -> Dict[str, Any]:
        """
        获取单个模型信息 - Stripchat平台特定功能
        
        Args:
            model_id: 模型ID
            
        Returns:
            模型详细信息
        """
        response = await self.raw_request(
            "GET",
            "/api/front/models/list",
            params={f"modelIds[0]": model_id}
        )
        
        models = response.get("models", [])
        if models:
            return models[0]
        else:
            raise PlatformException(f"未找到模型信息: {model_id}")
    
    async def get_models_batch(self, model_ids: List[str]) -> List[Dict[str, Any]]:
        """
        批量获取模型信息 - Stripchat平台特定功能
        
        Args:
            model_ids: 模型ID列表（最多50个）
            
        Returns:
            模型信息列表
        """
        if len(model_ids) > 50:
            raise PlatformException("最多支持50个模型ID")
        
        params = {}
        for i, model_id in enumerate(model_ids):
            params[f"modelIds[{i}]"] = model_id
        
        response = await self.raw_request(
            "GET",
            "/api/front/models/list",
            params=params
        )
        
        return response.get("models", [])
    
    async def search_models(
        self,
        primary_tag: str = "girls",
        filter_tags: Optional[List[str]] = None,
        sort_by: str = "stripRanking",
        page: int = 1,
        size: int = 20
    ) -> List[Dict[str, Any]]:
        """
        搜索模型 - Stripchat平台特定功能
        
        Args:
            primary_tag: 主要标签
            filter_tags: 过滤标签列表
            sort_by: 排序方式
            page: 页码
            size: 每页数量
            
        Returns:
            搜索结果列表
        """
        offset = (page - 1) * size
        
        params = {
            "improveTs": "false",
            "removeShows": "false",
            "limit": min(size, 60),
            "offset": offset,
            "primaryTag": primary_tag,
            "sortBy": sort_by,
            "rcmGrp": "A",
            "rbCnGr": "false"
        }
        
        if filter_tags:
            params["filterGroupTags"] = f'[["{filter_tags[0]}"]]' if filter_tags else ""
        
        response = await self.raw_request(
            "GET",
            "/api/front/models",
            params=params
        )
        
        return response.get("models", [])
    
    async def get_featured_models(self, page: int = 1, size: int = 20) -> List[Dict[str, Any]]:
        """
        获取精选模型 - Stripchat平台特定功能
        
        Args:
            page: 页码
            size: 每页数量
            
        Returns:
            精选模型列表
        """
        return await self.search_models(
            primary_tag="girls",
            sort_by="stripRanking",
            page=page,
            size=size
        )
    
    async def get_new_models(self, page: int = 1, size: int = 20) -> List[Dict[str, Any]]:
        """
        获取新人模型 - Stripchat平台特定功能
        
        Args:
            page: 页码
            size: 每页数量
            
        Returns:
            新人模型列表
        """
        return await self.search_models(
            primary_tag="girls",
            filter_tags=["autoTagNew"],
            sort_by="trending",
            page=page,
            size=size
        )
    
    async def get_models_by_language(
        self,
        language: str,
        page: int = 1,
        size: int = 20
    ) -> List[Dict[str, Any]]:
        """
        按语言获取模型 - Stripchat平台特定功能
        
        Args:
            language: 语言标签（如 tagLanguageChinese, tagLanguageJapanese）
            page: 页码
            size: 每页数量
            
        Returns:
            指定语言的模型列表
        """
        return await self.search_models(
            primary_tag="girls",
            filter_tags=[language],
            sort_by="stripRanking",
            page=page,
            size=size
        ) 