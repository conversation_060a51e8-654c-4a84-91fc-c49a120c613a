"""
直播聚合核心库 - 数据模型

使用 Pydantic 定义统一的数据模型
"""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, Generic, List, Optional, TypeVar

import orjson
from pydantic import BaseModel, ConfigDict, Field, field_validator

# 定义泛型类型
T = TypeVar('T', bound=BaseModel)


class Platform(str, Enum):
    """支持的直播平台"""
    SWAG = "swag"
    XIAOHULI = "xiaohuli" 
    FANQIE = "fanqie"
    LVCHA = "lvcha"
    PANDA = "panda"
    STRIPCHAT = "stripchat"


class StreamType(str, Enum):
    """直播流类型"""
    FLV = "flv"
    HLS = "hls"
    RTMP = "rtmp"
    WEBRTC = "webrtc"


class StreamQuality(BaseModel):
    """流质量信息"""
    quality: str = Field(..., description="画质标识: origin/1080p/720p/480p")
    url: str = Field(..., description="流地址")
    format: Optional[StreamType] = Field(None, description="流格式")
    bitrate: Optional[int] = Field(None, description="码率")
    resolution: Optional[str] = Field(None, description="分辨率")


class UnifiedStreamer(BaseModel):
    """统一的主播数据模型"""
    model_config = ConfigDict(
        extra="allow",  # 允许额外字段
        json_encoders={
            datetime: lambda v: v.isoformat()
        }
    )
    
    # 核心字段（所有平台都有）
    id: str = Field(..., description="唯一标识: platform_userid")
    platform: Platform
    platform_user_id: str = Field(..., description="平台用户ID")
    platform_room_id: Optional[str] = Field(None, description="平台房间ID")
    
    # 基本信息
    username: str = Field(..., description="用户名")
    nickname: str = Field(..., description="昵称")
    avatar_url: Optional[str] = None
    cover_url: Optional[str] = None
    
    # 直播状态
    is_live: bool = False
    title: Optional[str] = None
    viewer_count: int = Field(default=0, ge=0)
    start_time: Optional[datetime] = None
    category: Optional[str] = None
    tags: List[str] = Field(default_factory=list)
    
    # 流信息
    stream_qualities: List[StreamQuality] = Field(default_factory=list)
    
    # 访问限制
    is_vip: bool = False
    need_password: bool = False
    need_login: bool = False
    
    # 原始数据和扩展字段
    raw_data: Optional[Dict[str, Any]] = Field(
        None, 
        description="原始API响应数据"
    )
    extra_fields: Dict[str, Any] = Field(
        default_factory=dict,
        description="平台特定的额外字段"
    )
    
    # 元数据
    last_updated: datetime = Field(default_factory=datetime.now)
    
    @field_validator('viewer_count')
    @classmethod
    def validate_viewer_count(cls, v: int) -> int:
        """确保观看人数非负"""
        return max(0, v)
    
    def get_best_stream_url(self) -> Optional[str]:
        """获取最佳质量的流地址"""
        if not self.stream_qualities:
            return None
        
        # 优先级: origin > 1080p > 720p > 480p
        priority_map = {
            "origin": 0, "原画": 0,
            "1080p": 1, "超清": 1,
            "720p": 2, "高清": 2,
            "480p": 3, "标清": 3
        }
        
        sorted_qualities = sorted(
            self.stream_qualities,
            key=lambda x: priority_map.get(x.quality.lower(), 999)
        )
        return sorted_qualities[0].url if sorted_qualities else None
    
    def to_json(self) -> str:
        """使用orjson快速序列化"""
        return orjson.dumps(self.model_dump()).decode()
    
    def get_stream_by_quality(self, quality: str) -> Optional[StreamQuality]:
        """根据画质获取流信息"""
        for stream in self.stream_qualities:
            if stream.quality.lower() == quality.lower():
                return stream
        return None


class ResponseWrapper(BaseModel, Generic[T]):
    """单个数据的响应包装器"""
    data: T  # 统一格式的数据
    raw: Dict[str, Any]  # 原始响应数据
    platform: str  # 平台标识
    timestamp: datetime = Field(default_factory=datetime.now)


class ListResponse(BaseModel, Generic[T]):
    """列表数据的响应包装器"""
    items: List[T]  # 统一格式的列表
    raw_items: List[Dict[str, Any]]  # 原始数据列表
    total: Optional[int] = None  # 总数
    page: Optional[int] = None  # 当前页
    page_size: Optional[int] = None  # 每页大小
    has_more: bool = False  # 是否有更多数据
    platform: str  # 平台标识
    timestamp: datetime = Field(default_factory=datetime.now)
    
    def get_raw_by_index(self, index: int) -> Optional[Dict[str, Any]]:
        """根据索引获取原始数据"""
        if 0 <= index < len(self.raw_items):
            return self.raw_items[index]
        return None
    
    def get_raw_by_id(self, streamer_id: str) -> Optional[Dict[str, Any]]:
        """根据ID获取原始数据"""
        for i, item in enumerate(self.items):
            if hasattr(item, 'id') and item.id == streamer_id:
                return self.get_raw_by_index(i)
        return None 