"""
Live Streams Core - 基本使用示例

演示如何使用核心库访问直播平台数据
"""

import asyncio
import sys
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from live_streams_core import Platform
from live_streams_core.platforms.swag import SwagAdapter


async def basic_example():
    """基本使用示例"""
    print("=== Live Streams Core 基本使用示例 ===\n")
    
    # 创建平台适配器
    adapter = SwagAdapter()
    
    # 获取直播列表（统一格式）
    print("1. 获取热门直播列表...")
    streamers = await adapter.get_live_list(category="hot", size=5)
    
    print(f"找到 {len(streamers)} 个主播:")
    for i, streamer in enumerate(streamers, 1):
        print(f"\n  {i}. {streamer.nickname}")
        print(f"     - ID: {streamer.id}")
        print(f"     - 用户名: {streamer.username}")
        print(f"     - 在线: {'是' if streamer.is_live else '否'}")
        print(f"     - 观看人数: {streamer.viewer_count}")
        print(f"     - 标题: {streamer.title}")
        if streamer.stream_qualities:
            print(f"     - 流地址: {streamer.get_best_stream_url()}")
    
    # 获取带原始数据的响应
    print("\n\n2. 获取带原始数据的响应...")
    response = await adapter.get_live_list_with_raw(category="new", size=3)
    
    print(f"总数: {response.total}")
    print(f"当前页: {response.page}")
    print(f"是否有更多: {response.has_more}")
    
    # 访问原始数据
    if response.items:
        first_item = response.items[0]
        raw_data = response.get_raw_by_index(0)
        
        print(f"\n第一个主播的统一数据:")
        print(f"  - 昵称: {first_item.nickname}")
        print(f"  - 平台: {first_item.platform.value}")
        
        print(f"\n对应的原始数据字段:")
        print(f"  - nickname: {raw_data.get('nickname')}")
        print(f"  - userId: {raw_data.get('userId')}")
        print(f"  - level: {raw_data.get('level', 'N/A')}")
    
    # 使用平台特定功能
    print("\n\n3. 使用平台特定功能...")
    categories = await adapter.get_categories()
    print("Swag平台分类:")
    for cat in categories:
        print(f"  - {cat['name']} ({cat['name_en']}): {cat['id']}")
    
    # 搜索功能（如果有）
    print("\n4. 搜索主播...")
    search_results = await adapter.search_streamers("美", size=3)
    print(f"搜索结果: {len(search_results)} 个")
    for streamer in search_results:
        print(f"  - {streamer.nickname} (@{streamer.username})")
    
    # 关闭适配器
    await adapter.__aexit__(None, None, None)


async def stream_url_example():
    """获取流地址示例"""
    print("\n\n=== 获取流地址示例 ===\n")
    
    adapter = SwagAdapter()
    
    # 先获取一个在线的主播
    streamers = await adapter.get_live_list(size=10)
    live_streamers = [s for s in streamers if s.is_live]
    
    if not live_streamers:
        print("没有找到在线的主播")
        return
    
    # 获取第一个在线主播的流地址
    streamer = live_streamers[0]
    print(f"获取主播 {streamer.nickname} 的流地址...")
    
    stream_qualities = await adapter.get_stream_url(streamer.platform_room_id)
    
    if stream_qualities:
        print(f"找到 {len(stream_qualities)} 个流地址:")
        for quality in stream_qualities:
            print(f"  - 画质: {quality.quality}")
            print(f"    格式: {quality.format.value if quality.format else 'unknown'}")
            print(f"    地址: {quality.url}")
    else:
        print("未找到流地址")
    
    await adapter.__aexit__(None, None, None)


async def error_handling_example():
    """错误处理示例"""
    print("\n\n=== 错误处理示例 ===\n")
    
    from live_streams_core import NetworkException, TimeoutError
    
    adapter = SwagAdapter()
    
    try:
        # 尝试获取不存在的房间
        await adapter.get_stream_url("invalid_room_id_12345")
    except NetworkException as e:
        print(f"网络错误: {e}")
    except TimeoutError as e:
        print(f"请求超时: {e}")
    except Exception as e:
        print(f"其他错误: {type(e).__name__}: {e}")
    
    await adapter.__aexit__(None, None, None)


async def concurrent_example():
    """并发请求示例"""
    print("\n\n=== 并发请求示例 ===\n")
    
    # 创建多个适配器实例
    adapter = SwagAdapter()
    
    # 并发获取不同分类的数据
    categories = ["hot", "new", "taiwan"]
    
    print(f"并发获取 {len(categories)} 个分类的数据...")
    
    tasks = [
        adapter.get_live_list(category=cat, size=5)
        for cat in categories
    ]
    
    results = await asyncio.gather(*tasks)
    
    for cat, streamers in zip(categories, results):
        print(f"\n{cat} 分类: {len(streamers)} 个主播")
        if streamers:
            print(f"  第一个: {streamers[0].nickname}")
    
    await adapter.__aexit__(None, None, None)


async def main():
    """主函数"""
    try:
        # 运行基本示例
        await basic_example()
        
        # 运行流地址示例
        await stream_url_example()
        
        # 运行错误处理示例
        await error_handling_example()
        
        # 运行并发示例
        await concurrent_example()
        
    except KeyboardInterrupt:
        print("\n\n用户中断")
    except Exception as e:
        print(f"\n\n发生错误: {type(e).__name__}: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # Windows下的事件循环设置
    if sys.platform == "win32":
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    
    # 运行主函数
    asyncio.run(main()) 